"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// API调用函数\nconst fetchCourseList = async (seriesId)=>{\n    const response = await fetch(\"/api/v1/course-management/series/\".concat(seriesId, \"/courses\"));\n    if (!response.ok) {\n        throw new Error(\"Failed to fetch course list\");\n    }\n    return response.json();\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            loadCourseList();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            const response = await fetchCourseList(seriesId);\n            if (response.code === 200) {\n                setCourseList(response.data.list);\n            }\n        } catch (error) {\n            console.error(\"Failed to load course list:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 删除课程\n    const deleteCourse = (id)=>{\n        setCourseList(courseList.filter((course)=>course.id !== id));\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 保存课程列表\n    const handleSave = ()=>{\n        const data = {\n            title: editingTitle,\n            courseGoals,\n            courseObjectives,\n            courseList\n        };\n        onSave(data);\n        onClose();\n    };\n    // 发布系列课程\n    const handlePublish = ()=>{\n        // TODO: 实现发布逻辑\n        alert(\"发布系列课程功能待实现\");\n    };\n    // 退出编辑模式\n    const handleExitEdit = ()=>{\n        onClose();\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-list-modal\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-title-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"course-list-title\",\n                                    children: \"课程列表\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"course-list-settings-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-add-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 176,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 169,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"course-list-close-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 168,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-sidebar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-items\",\n                                children: courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-item\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"course-list-item-text\",\n                                                children: course.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>deleteCourse(course.id),\n                                                className: \"course-list-item-delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 191,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-edit-area\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-series-cover\",\n                                    children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: seriesCoverImage,\n                                        alt: \"系列课程封面\",\n                                        className: \"course-series-cover-image\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-series-cover-placeholder\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"系列课程封面\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-edit-form\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"course-edit-label\",\n                                                    children: \"系列课程标题\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    value: editingTitle,\n                                                    onChange: (e)=>setEditingTitle(e.target.value),\n                                                    className: \"course-edit-input\",\n                                                    placeholder: \"请输入系列课程标题\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 226,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 224,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"course-edit-label\",\n                                                    children: \"课程标签\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-input-group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: courseGoals,\n                                                            onChange: (e)=>setCourseGoals(e.target.value),\n                                                            className: \"course-edit-input-small\",\n                                                            placeholder: \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"course-edit-add-btn\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 247,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-field\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"course-edit-label\",\n                                                    children: \"课程项目成员\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-input-group\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: courseObjectives,\n                                                            onChange: (e)=>setCourseObjectives(e.target.value),\n                                                            className: \"course-edit-input-small\",\n                                                            placeholder: \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            className: \"course-edit-add-btn\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                                className: \"w-4 h-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                lineNumber: 264,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-footer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePublish,\n                                className: \"course-list-btn course-list-btn-publish\",\n                                children: \"发布系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 275,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 274,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExitEdit,\n                                    className: \"course-list-btn course-list-btn-exit\",\n                                    children: \"退出编辑模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 280,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"course-list-btn course-list-btn-save\",\n                                    children: \"保存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 273,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n            lineNumber: 166,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 165,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"sdV3ZAyK7z900ef1eO+3R1ad/vY=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});