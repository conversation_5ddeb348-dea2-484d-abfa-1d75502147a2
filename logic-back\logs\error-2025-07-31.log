2025-07-31 10:01:11.067 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 10:01:11.138 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.140 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.140 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.140 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.141 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.141 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.141 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.142 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.142 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.142 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.143 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.143 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.143 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.144 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.144 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.144 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
