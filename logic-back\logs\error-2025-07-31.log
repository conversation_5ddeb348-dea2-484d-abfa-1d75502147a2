2025-07-31 10:01:11.067 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 10:01:11.138 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.139 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.140 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.140 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.140 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.141 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.141 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.141 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.142 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.142 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.142 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.143 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.143 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.143 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.144 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.144 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 10:01:11.144 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":6364,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.223 [ERROR] [KeyManagementService] 保存RSA密钥对到Redis失败: Cannot read properties of undefined (reading 'set') {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":["TypeError: Cannot read properties of undefined (reading 'set')\n    at RedisService.set (D:\\logicleap\\logic-back\\src\\util\\database\\redis\\redis.service.ts:41:31)\n    at KeyManagementService.saveRsaKeyPairToRedis (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:533:31)\n    at KeyManagementService.generateNewRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:514:18)\n    at KeyManagementService.getRsaKeyPair (D:\\logicleap\\logic-back\\src\\util\\encrypt\\key-management\\key-management.service.ts:578:12)\n    at new EncryptionService (D:\\logicleap\\logic-back\\src\\util\\encrypt\\encryption.service.ts:62:50)\n    at Injector.instantiateClass (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:373:19)\n    at callback (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:65:45)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Injector.resolveConstructorParams (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:145:24)\n    at async Injector.loadInstance (D:\\logicleap\\logic-back\\node_modules\\@nestjs\\core\\injector\\injector.js:70:13)"]}
2025-07-31 11:01:30.459 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.460 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.461 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.462 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.463 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.463 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.463 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.464 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.464 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.465 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.465 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.466 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.467 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.468 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.468 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.469 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.469 [ERROR] Duplicate DTO detected: "CourseSettingsResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.470 [ERROR] Duplicate DTO detected: "CreateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.470 [ERROR] Duplicate DTO detected: "UpdateTagDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:01:30.471 [ERROR] Duplicate DTO detected: "ErrorResponseDto" is defined multiple times with different schemas.
Consider using unique class names or applying @ApiExtraModels() decorator with custom schema names.
Note: This will throw an error in the next major version. {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY","stack":[null]}
2025-07-31 11:04:19.636 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:04:19.637 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:04:19.636Z"}
2025-07-31 11:05:18.671 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:05:18.672 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:05:18.671Z"}
2025-07-31 11:05:19.787 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:05:19.788 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:05:19.787Z"}
2025-07-31 11:06:46.623 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:06:46.625 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:06:46.624Z"}
2025-07-31 11:06:46.645 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:06:46.646 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:06:46.646Z"}
2025-07-31 11:06:48.059 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:06:48.062 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:06:48.061Z"}
2025-07-31 11:06:48.134 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:06:48.136 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:06:48.136Z"}
2025-07-31 11:07:48.120 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:07:48.122 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:07:48.121Z"}
2025-07-31 11:07:48.159 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:07:48.160 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:07:48.160Z"}
2025-07-31 11:07:55.467 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:07:55.477 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:07:55.471Z"}
2025-07-31 11:08:48.160 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:08:48.164 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:08:48.160Z"}
2025-07-31 11:09:07.388 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:09:07.390 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:09:07.389Z"}
2025-07-31 11:09:07.428 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:09:07.429 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:09:07.429Z"}
2025-07-31 11:09:09.351 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:09:09.353 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:09:09.352Z"}
2025-07-31 11:10:26.905 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:10:26.906 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:10:26.905Z"}
2025-07-31 11:10:49.485 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:10:49.486 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:10:49.486Z"}
2025-07-31 11:11:26.942 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:11:26.944 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:11:26.943Z"}
2025-07-31 11:12:26.967 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:12:26.968 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:12:26.967Z"}
2025-07-31 11:13:27.003 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:13:27.004 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:13:27.004Z"}
2025-07-31 11:14:27.030 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:14:27.031 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:14:27.031Z"}
2025-07-31 11:15:27.075 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:15:27.076 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:15:27.075Z"}
2025-07-31 11:16:27.109 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:16:27.117 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:16:27.112Z"}
2025-07-31 11:17:27.146 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:17:27.147 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:17:27.147Z"}
2025-07-31 11:18:27.171 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:18:27.172 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:18:27.171Z"}
2025-07-31 11:19:27.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:19:27.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:19:27.247Z"}
2025-07-31 11:20:28.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:20:28.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:20:28.247Z"}
2025-07-31 11:21:29.261 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:21:29.262 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:21:29.261Z"}
2025-07-31 11:22:30.253 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:22:30.254 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:22:30.253Z"}
2025-07-31 11:23:31.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:23:31.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:23:31.244Z"}
2025-07-31 11:24:32.276 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:24:32.287 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:24:32.277Z"}
2025-07-31 11:24:59.367 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/series/123/courses - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-07-31 11:24:59.367 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/v1/course-management/series/123/courses","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:24:59.367Z"}
2025-07-31 11:24:59.384 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/series/123/courses - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-07-31 11:24:59.387 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/v1/course-management/series/123/courses","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:24:59.386Z"}
2025-07-31 11:25:33.259 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:25:33.260 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:25:33.260Z"}
2025-07-31 11:26:06.323 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/series/3483/courses - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-07-31 11:26:06.324 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/v1/course-management/series/3483/courses","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:26:06.323Z"}
2025-07-31 11:26:33.281 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:26:33.283 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:26:33.282Z"}
2025-07-31 11:26:36.965 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:26:36.966 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:26:36.966Z"}
2025-07-31 11:27:34.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:27:34.246 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:27:34.246Z"}
2025-07-31 11:28:35.246 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:28:35.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:28:35.247Z"}
2025-07-31 11:29:36.255 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:29:36.259 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:29:36.256Z"}
2025-07-31 11:30:37.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:30:37.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:30:37.245Z"}
2025-07-31 11:31:38.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:31:38.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:31:38.247Z"}
2025-07-31 11:32:39.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:32:39.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:32:39.247Z"}
2025-07-31 11:33:40.249 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:33:40.250 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:33:40.249Z"}
2025-07-31 11:34:41.244 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:34:41.244 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:34:41.244Z"}
2025-07-31 11:35:42.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:35:42.248 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:35:42.248Z"}
2025-07-31 11:36:43.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:36:43.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:36:43.236Z"}
2025-07-31 11:37:44.255 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:37:44.255 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:37:44.255Z"}
2025-07-31 11:38:45.236 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:38:45.237 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:38:45.237Z"}
2025-07-31 11:39:46.238 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:39:46.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:39:46.238Z"}
2025-07-31 11:40:47.254 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:40:47.254 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:40:47.254Z"}
2025-07-31 11:41:48.254 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:41:48.254 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:41:48.254Z"}
2025-07-31 11:42:49.237 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:42:49.238 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:42:49.238Z"}
2025-07-31 11:43:50.245 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:43:50.245 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:43:50.245Z"}
2025-07-31 11:44:51.242 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:44:51.242 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:44:51.242Z"}
2025-07-31 11:45:52.285 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:45:52.286 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:45:52.286Z"}
2025-07-31 11:46:19.503 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/series/2571/courses - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-07-31 11:46:19.505 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/v1/course-management/series/2571/courses","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:46:19.504Z"}
2025-07-31 11:46:19.526 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/series/2571/courses - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-07-31 11:46:19.526 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/v1/course-management/series/2571/courses","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:46:19.526Z"}
2025-07-31 11:46:22.006 [ERROR] [GlobalExceptionFilter] GET /api/v1/course-management/series/2554/courses - Http Exception {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
HttpException: Http Exception
    at RouterGuardService.validateUserAndGetInfo (D:\logicleap\logic-back\src\web\router_guard\router-guard.service.ts:84:13)
    at ApiAuthGuard.canActivate (D:\logicleap\logic-back\src\web\router_guard\api-auth.guard.ts:66:52)
    at GuardsConsumer.tryActivate (D:\logicleap\logic-back\node_modules\@nestjs\core\guards\guards-consumer.js:15:34)
    at canActivateFn (D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:135:59)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-execution-context.js:42:37
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at next (D:\logicleap\logic-back\node_modules\router\lib\route.js:157:13)
    at Route.dispatch (D:\logicleap\logic-back\node_modules\router\lib\route.js:117:3)
    at handle (D:\logicleap\logic-back\node_modules\router\index.js:435:11)
2025-07-31 11:46:22.007 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/v1/course-management/series/2554/courses","method":"GET","statusCode":401,"message":"Http Exception","details":{"code":401,"msg":"请先登录","data":null},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:46:22.007Z"}
2025-07-31 11:46:53.235 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:46:53.236 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:46:53.235Z"}
2025-07-31 11:47:54.247 [ERROR] [GlobalExceptionFilter] GET /api/encryption/public-key - Cannot GET /api/encryption/public-key {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
NotFoundException: Cannot GET /api/encryption/public-key
    at callback (D:\logicleap\logic-back\node_modules\@nestjs\core\router\routes-resolver.js:77:19)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
    at trimPrefix (D:\logicleap\logic-back\node_modules\router\index.js:342:13)
    at D:\logicleap\logic-back\node_modules\router\index.js:297:9
    at processParams (D:\logicleap\logic-back\node_modules\router\index.js:582:12)
    at next (D:\logicleap\logic-back\node_modules\router\index.js:291:5)
    at HttpLoggerMiddleware.use (D:\logicleap\logic-back\src\common\logger\http-logger.middleware.ts:21:5)
    at D:\logicleap\logic-back\node_modules\@nestjs\core\router\router-proxy.js:9:23
    at Layer.handleRequest (D:\logicleap\logic-back\node_modules\router\lib\layer.js:152:17)
2025-07-31 11:47:54.247 [ERROR] [GlobalExceptionFilter] Exception Details {"service":"logic-back","version":"0.0.1","environment":"dev","pid":34908,"hostname":"QQY"}
{"url":"/api/encryption/public-key","method":"GET","statusCode":404,"message":"Cannot GET /api/encryption/public-key","details":{"message":"Cannot GET /api/encryption/public-key","error":"Not Found","statusCode":404},"userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 SLBrowser/9.0.6.5061 SLBChan/109 SLBVPV/64-bit","ip":"::ffff:127.0.0.1","timestamp":"2025-07-31T03:47:54.247Z"}
