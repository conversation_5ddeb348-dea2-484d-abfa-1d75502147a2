"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// API调用函数\nconst fetchCourseList = async (seriesId)=>{\n    return await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.getSeriesCourses(seriesId);\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _getSelectedCourse4;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                return;\n            }\n            loadCourseList();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            // 根据seriesId生成不同的模拟数据\n            const generateMockData = (seriesId)=>{\n                const courseTemplates = [\n                    // 系列1: React开发系列\n                    {\n                        seriesId: 1,\n                        courses: [\n                            {\n                                title: \"第一课：React基础入门\",\n                                description: \"学习React的基本概念和组件开发\",\n                                duration: \"30分钟\"\n                            },\n                            {\n                                title: \"第二课：组件与Props\",\n                                description: \"深入理解React组件和Props的使用\",\n                                duration: \"35分钟\"\n                            },\n                            {\n                                title: \"第三课：状态管理\",\n                                description: \"学习React中的状态管理和生命周期\",\n                                duration: \"45分钟\"\n                            }\n                        ]\n                    },\n                    // 系列2: Vue开发系列\n                    {\n                        seriesId: 2,\n                        courses: [\n                            {\n                                title: \"第一课：Vue基础语法\",\n                                description: \"掌握Vue的基本语法和指令\",\n                                duration: \"28分钟\"\n                            },\n                            {\n                                title: \"第二课：组件化开发\",\n                                description: \"学习Vue组件的创建和使用\",\n                                duration: \"40分钟\"\n                            },\n                            {\n                                title: \"第三课：路由与状态管理\",\n                                description: \"Vue Router和Vuex的使用\",\n                                duration: \"50分钟\"\n                            }\n                        ]\n                    },\n                    // 系列3: Node.js后端开发\n                    {\n                        seriesId: 3,\n                        courses: [\n                            {\n                                title: \"第一课：Node.js环境搭建\",\n                                description: \"安装配置Node.js开发环境\",\n                                duration: \"25分钟\"\n                            },\n                            {\n                                title: \"第二课：Express框架入门\",\n                                description: \"使用Express构建Web应用\",\n                                duration: \"42分钟\"\n                            },\n                            {\n                                title: \"第三课：数据库操作\",\n                                description: \"MongoDB和MySQL的使用\",\n                                duration: \"55分钟\"\n                            }\n                        ]\n                    },\n                    // 系列4: Python数据分析\n                    {\n                        seriesId: 4,\n                        courses: [\n                            {\n                                title: \"第一课：Python基础语法\",\n                                description: \"掌握Python编程基础\",\n                                duration: \"32分钟\"\n                            },\n                            {\n                                title: \"第二课：NumPy数组操作\",\n                                description: \"学习NumPy库的使用\",\n                                duration: \"38分钟\"\n                            },\n                            {\n                                title: \"第三课：Pandas数据处理\",\n                                description: \"使用Pandas进行数据分析\",\n                                duration: \"48分钟\"\n                            }\n                        ]\n                    }\n                ];\n                // 查找对应系列的课程模板\n                const template = courseTemplates.find((t)=>t.seriesId === seriesId);\n                // 如果没有找到对应模板，使用默认模板\n                const courses = template ? template.courses : [\n                    {\n                        title: \"系列\".concat(seriesId, \" - 第一课：基础入门\"),\n                        description: \"系列\".concat(seriesId, \"的基础课程\"),\n                        duration: \"30分钟\"\n                    },\n                    {\n                        title: \"系列\".concat(seriesId, \" - 第二课：进阶学习\"),\n                        description: \"系列\".concat(seriesId, \"的进阶课程\"),\n                        duration: \"40分钟\"\n                    },\n                    {\n                        title: \"系列\".concat(seriesId, \" - 第三课：实战应用\"),\n                        description: \"系列\".concat(seriesId, \"的实战课程\"),\n                        duration: \"50分钟\"\n                    }\n                ];\n                return {\n                    code: 200,\n                    message: \"success\",\n                    data: {\n                        list: courses.map((course, index)=>({\n                                id: seriesId * 100 + index + 1,\n                                seriesId: seriesId,\n                                title: course.title,\n                                description: course.description,\n                                coverImage: \"https://example.com/series\".concat(seriesId, \"-course\").concat(index + 1, \"-cover.jpg\"),\n                                orderIndex: index + 1,\n                                status: index === 0 ? 1 : index === 1 ? 0 : 1,\n                                statusLabel: index === 0 ? \"已发布\" : index === 1 ? \"草稿\" : \"已发布\",\n                                hasVideo: 1,\n                                hasDocument: index % 2 === 0 ? 1 : 0,\n                                hasAudio: index % 3 === 0 ? 1 : 0,\n                                videoDuration: parseInt(course.duration) * 60,\n                                videoDurationLabel: course.duration,\n                                videoName: \"\".concat(course.title, \".mp4\"),\n                                firstTeachingTitle: \"教学目标\",\n                                resourcesCount: index + 2,\n                                createdAt: \"2024-01-\".concat(25 + index, \"T16:20:00Z\"),\n                                updatedAt: \"2024-01-\".concat(25 + index, \"T16:20:00Z\")\n                            })),\n                        pagination: {\n                            page: 1,\n                            pageSize: 20,\n                            total: courses.length,\n                            totalPages: 1,\n                            hasNext: false,\n                            hasPrev: false\n                        }\n                    }\n                };\n            };\n            const mockResponse = generateMockData(seriesId);\n            console.log(\"\\uD83D\\uDCE1 使用模拟数据:\", mockResponse);\n            setCourseList(mockResponse.data.list);\n        // 注释掉真实API调用，稍后再启用\n        // const response = await fetchCourseList(seriesId);\n        // console.log('📡 API响应:', response);\n        // if (response.code === 200) {\n        //   console.log('✅ 课程列表数据:', response.data.list);\n        //   setCourseList(response.data.list);\n        // } else {\n        //   console.error('❌ API返回错误:', response);\n        // }\n        } catch (error) {\n            console.error(\"❌ 加载课程列表失败:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 删除课程\n    const deleteCourse = (id)=>{\n        setCourseList(courseList.filter((course)=>course.id !== id));\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 保存课程列表\n    const handleSave = ()=>{\n        const data = {\n            title: editingTitle,\n            courseGoals,\n            courseObjectives,\n            courseList\n        };\n        onSave(data);\n        onClose();\n    };\n    // 发布系列课程\n    const handlePublish = ()=>{\n        // TODO: 实现发布逻辑\n        alert(\"发布系列课程功能待实现\");\n    };\n    // 退出编辑模式\n    const handleExitEdit = ()=>{\n        onClose();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-list-modal\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-title-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"course-list-title\",\n                                    children: \"课程列表\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: showSettingsPanel,\n                                            className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-add-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 291,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"course-list-close-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-sidebar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-items\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 316,\n                                    columnNumber: 17\n                                }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-list-empty-title\",\n                                            children: \"暂无课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-list-empty-description\",\n                                            children: \"点击右上角的 + 按钮添加第一个课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-empty-btn\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"添加课时\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 17\n                                }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                        onClick: ()=>showCoursePanel(course.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"course-list-item-text\",\n                                                children: course.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteCourse(course.id);\n                                                },\n                                                className: \"course-list-item-delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 351,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-edit-area\",\n                            children: [\n                                rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-edit-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 364,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 363,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-edit-empty-title\",\n                                            children: \"无课程详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-edit-empty-description\",\n                                            children: \"点击左侧课程或设置按钮查看详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, undefined),\n                                rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: seriesCoverImage,\n                                                alt: \"系列课程封面\",\n                                                className: \"course-series-cover-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"系列课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editingTitle,\n                                                            onChange: (e)=>setEditingTitle(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 393,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-edit-input-group\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"text\",\n                                                                    value: courseGoals,\n                                                                    onChange: (e)=>setCourseGoals(e.target.value),\n                                                                    className: \"course-edit-input-small\",\n                                                                    placeholder: \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 408,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"course-edit-add-btn\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程项目成员\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"course-edit-input-group\",\n                                                            children: [\n                                                                'type=\"text\" value=',\n                                                                courseObjectives,\n                                                                \"onChange=\",\n                                                                (e)=>setCourseObjectives(e.target.value),\n                                                                'className=\"course-edit-input-small\" placeholder=\"\" />',\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"course-edit-add-btn\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                        className: \"w-4 h-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 25\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                    lineNumber: 431,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 444,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.title) || \"\",\n                                                            onChange: (e)=>updateCourseTitle(selectedCourseId, e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 454,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"course-edit-textarea\",\n                                                            placeholder: \"请输入课程描述\",\n                                                            rows: 4,\n                                                            value: ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.description) || \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 466,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 464,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 476,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-label\",\n                                                            children: (_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.statusLabel\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 475,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.hasVideo) === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"视频时长\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-video-duration\",\n                                                            children: (_getSelectedCourse4 = getSelectedCourse()) === null || _getSelectedCourse4 === void 0 ? void 0 : _getSelectedCourse4.videoDurationLabel\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 360,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-footer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePublish,\n                                className: \"course-list-btn course-list-btn-publish\",\n                                children: \"发布系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExitEdit,\n                                    className: \"course-list-btn course-list-btn-exit\",\n                                    children: \"退出编辑模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"course-list-btn course-list-btn-save\",\n                                    children: \"保存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 508,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 504,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 498,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n            lineNumber: 288,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 287,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"sdV3ZAyK7z900ef1eO+3R1ad/vY=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ })

});