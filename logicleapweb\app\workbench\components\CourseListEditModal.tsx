'use client';

import React, { useState, useEffect } from 'react';
import { X, Settings, Plus, BookOpen, FileText } from 'lucide-react';
import { courseManagementApi } from '@/lib/api/course-management';
import './CourseListEditModal.css';

interface CourseItem {
  id: number;
  seriesId: number;
  title: string;
  description: string;
  coverImage: string;
  orderIndex: number;
  status: number;
  statusLabel: string;
  hasVideo: number;
  hasDocument: number;
  hasAudio: number;
  videoDuration: number;
  videoDurationLabel: string;
  videoName: string;
  firstTeachingTitle: string;
  resourcesCount: number;
  createdAt: string;
  updatedAt: string;
}

interface ApiResponse {
  code: number;
  message: string;
  data: {
    list: CourseItem[];
    pagination: {
      page: number;
      pageSize: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
}

interface CourseListEditModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSave: (data: any) => void;
  seriesTitle: string;
  seriesCoverImage?: string;
  seriesId: number;
}

// API调用函数
const fetchCourseList = async (seriesId: number): Promise<ApiResponse> => {
  return await courseManagementApi.getSeriesCourses(seriesId);
};

const CourseListEditModal: React.FC<CourseListEditModalProps> = ({
  isVisible,
  onClose,
  onSave,
  seriesTitle,
  seriesCoverImage,
  seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId
}) => {
  const [courseList, setCourseList] = useState<CourseItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [rightPanelType, setRightPanelType] = useState<'none' | 'settings' | 'course'>('none');
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);

  const [editingTitle, setEditingTitle] = useState(seriesTitle);
  const [courseGoals, setCourseGoals] = useState('');
  const [courseObjectives, setCourseObjectives] = useState('');

  // 获取课程列表数据
  useEffect(() => {
    if (isVisible && seriesId) {
      // 检查用户登录状态
      const token = localStorage.getItem('token');
      console.log('🔐 检查登录状态，token存在:', !!token);

      if (!token) {
        console.error('❌ 用户未登录，无法获取课程列表');
        return;
      }

      loadCourseList();
    }
  }, [isVisible, seriesId]);

  const loadCourseList = async () => {
    try {
      setLoading(true);
      console.log('🔍 开始加载课程列表，seriesId:', seriesId);

      // 临时使用模拟数据进行测试
      const mockResponse = {
        code: 200,
        message: "success",
        data: {
          list: [
            {
              id: 10,
              seriesId: seriesId,
              title: "第一课：React基础入门",
              description: "学习React的基本概念和组件开发",
              coverImage: "https://example.com/course1-cover.jpg",
              orderIndex: 1,
              status: 1,
              statusLabel: "已发布",
              hasVideo: 1,
              hasDocument: 1,
              hasAudio: 0,
              videoDuration: 1800,
              videoDurationLabel: "30分钟",
              videoName: "React基础入门.mp4",
              firstTeachingTitle: "教学目标",
              resourcesCount: 2,
              createdAt: "2024-01-25T16:20:00Z",
              updatedAt: "2024-01-25T16:20:00Z"
            },
            {
              id: 11,
              seriesId: seriesId,
              title: "第二课：组件与Props",
              description: "深入理解React组件和Props的使用",
              coverImage: "https://example.com/course2-cover.jpg",
              orderIndex: 2,
              status: 0,
              statusLabel: "草稿",
              hasVideo: 1,
              hasDocument: 1,
              hasAudio: 0,
              videoDuration: 2100,
              videoDurationLabel: "35分钟",
              videoName: "组件与Props.mp4",
              firstTeachingTitle: "教学目标",
              resourcesCount: 3,
              createdAt: "2024-01-26T10:30:00Z",
              updatedAt: "2024-01-26T10:30:00Z"
            },
            {
              id: 12,
              seriesId: seriesId,
              title: "第三课：状态管理",
              description: "学习React中的状态管理和生命周期",
              coverImage: "https://example.com/course3-cover.jpg",
              orderIndex: 3,
              status: 1,
              statusLabel: "已发布",
              hasVideo: 1,
              hasDocument: 0,
              hasAudio: 1,
              videoDuration: 2700,
              videoDurationLabel: "45分钟",
              videoName: "状态管理.mp4",
              firstTeachingTitle: "教学目标",
              resourcesCount: 4,
              createdAt: "2024-01-27T14:15:00Z",
              updatedAt: "2024-01-27T14:15:00Z"
            }
          ],
          pagination: {
            page: 1,
            pageSize: 20,
            total: 3,
            totalPages: 1,
            hasNext: false,
            hasPrev: false
          }
        }
      };

      console.log('📡 使用模拟数据:', mockResponse);
      setCourseList(mockResponse.data.list);

      // 注释掉真实API调用，稍后再启用
      // const response = await fetchCourseList(seriesId);
      // console.log('📡 API响应:', response);
      // if (response.code === 200) {
      //   console.log('✅ 课程列表数据:', response.data.list);
      //   setCourseList(response.data.list);
      // } else {
      //   console.error('❌ API返回错误:', response);
      // }
    } catch (error) {
      console.error('❌ 加载课程列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 添加新课程
  const addNewCourse = () => {
    const newCourse: CourseItem = {
      id: Date.now(),
      seriesId: seriesId,
      title: `第${courseList.length + 1}课 - 新课时`,
      description: '',
      coverImage: '',
      orderIndex: courseList.length + 1,
      status: 0,
      statusLabel: '草稿',
      hasVideo: 0,
      hasDocument: 0,
      hasAudio: 0,
      videoDuration: 0,
      videoDurationLabel: '',
      videoName: '',
      firstTeachingTitle: '',
      resourcesCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    setCourseList([...courseList, newCourse]);
    // 自动选中新添加的课程
    showCoursePanel(newCourse.id);
  };

  // 删除课程
  const deleteCourse = (id: number) => {
    setCourseList(courseList.filter(course => course.id !== id));
  };

  // 更新课程标题
  const updateCourseTitle = (id: number, newTitle: string) => {
    setCourseList(courseList.map(course => 
      course.id === id ? { ...course, title: newTitle } : course
    ));
  };

  // 保存课程列表
  const handleSave = () => {
    const data = {
      title: editingTitle,
      courseGoals,
      courseObjectives,
      courseList
    };
    onSave(data);
    onClose();
  };

  // 发布系列课程
  const handlePublish = () => {
    // TODO: 实现发布逻辑
    alert('发布系列课程功能待实现');
  };

  // 退出编辑模式
  const handleExitEdit = () => {
    onClose();
  };

  // 显示设置面板
  const showSettingsPanel = () => {
    setRightPanelType('settings');
    setSelectedCourseId(null);
  };

  // 显示课程编辑面板
  const showCoursePanel = (courseId: number) => {
    setRightPanelType('course');
    setSelectedCourseId(courseId);
  };

  // 获取选中的课程
  const getSelectedCourse = () => {
    return courseList.find(course => course.id === selectedCourseId);
  };

  if (!isVisible) return null;

  return (
    <div className="course-list-modal-overlay">
      <div className="course-list-modal">
        {/* 头部 */}
        <div className="course-list-header">
          <div className="course-list-title-section">
            <h2 className="course-list-title">课程列表</h2>
            <div className="course-list-actions">
              <button
                onClick={showSettingsPanel}
                className={`course-list-settings-btn ${rightPanelType === 'settings' ? 'active' : ''}`}
              >
                <Settings className="w-4 h-4" />
              </button>
              <button onClick={addNewCourse} className="course-list-add-btn">
                <Plus className="w-4 h-4" />
              </button>
            </div>
          </div>
          <button onClick={onClose} className="course-list-close-btn">
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* 主要内容 */}
        <div className="course-list-content">
          {/* 左侧课程列表 */}
          <div className="course-list-sidebar">
            <div className="course-list-items">
              {loading ? (
                <div className="course-list-loading">
                  <p>加载中...</p>
                </div>
              ) : courseList.length === 0 ? (
                <div className="course-list-empty">
                  <div className="course-list-empty-icon">
                    <BookOpen className="w-12 h-12 text-gray-300" />
                  </div>
                  <h3 className="course-list-empty-title">暂无课时</h3>
                  <p className="course-list-empty-description">
                    点击右上角的 + 按钮添加第一个课时
                  </p>
                  <button
                    onClick={addNewCourse}
                    className="course-list-empty-btn"
                  >
                    <Plus className="w-4 h-4" />
                    添加课时
                  </button>
                </div>
              ) : (
                courseList.map((course) => (
                  <div
                    key={course.id}
                    className={`course-list-item ${selectedCourseId === course.id ? 'active' : ''}`}
                    onClick={() => showCoursePanel(course.id)}
                  >
                    <span className="course-list-item-text">{course.title}</span>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        deleteCourse(course.id);
                      }}
                      className="course-list-item-delete"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 右侧编辑区域 */}
          <div className="course-list-edit-area">
            {rightPanelType === 'none' && (
              <div className="course-edit-empty">
                <div className="course-edit-empty-icon">
                  <FileText className="w-16 h-16 text-gray-300" />
                </div>
                <h3 className="course-edit-empty-title">无课程详情</h3>
                <p className="course-edit-empty-description">
                  点击左侧课程或设置按钮查看详情
                </p>
              </div>
            )}

            {rightPanelType === 'settings' && (
              <>
                {/* 系列课程封面 */}
                <div className="course-series-cover">
                  {seriesCoverImage ? (
                    <img
                      src={seriesCoverImage}
                      alt="系列课程封面"
                      className="course-series-cover-image"
                    />
                  ) : (
                    <div className="course-series-cover-placeholder">
                      <span>系列课程封面</span>
                    </div>
                  )}
                </div>

                {/* 系列设置表单 */}
                <div className="course-edit-form">
                  {/* 系列课程标题 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">系列课程标题</label>
                    <input
                      type="text"
                      value={editingTitle}
                      onChange={(e) => setEditingTitle(e.target.value)}
                      className="course-edit-input"
                      placeholder="请输入系列课程标题"
                    />
                  </div>

                  {/* 课程标签 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程标签</label>
                    <div className="course-edit-input-group">
                      <input
                        type="text"
                        value={courseGoals}
                        onChange={(e) => setCourseGoals(e.target.value)}
                        className="course-edit-input-small"
                        placeholder=""
                      />
                      <button className="course-edit-add-btn">
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* 课程项目成员 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程项目成员</label>
                    <div className="course-edit-input-group">
                        type="text"
                        value={courseObjectives}
                        onChange={(e) => setCourseObjectives(e.target.value)}
                        className="course-edit-input-small"
                        placeholder=""
                      />
                      <button className="course-edit-add-btn">
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </>
            )}

            {rightPanelType === 'course' && getSelectedCourse() && (
              <>
                {/* 课程封面 */}
                <div className="course-series-cover">
                  <div className="course-series-cover-placeholder">
                    <span>课程封面</span>
                  </div>
                </div>

                {/* 课程编辑表单 */}
                <div className="course-edit-form">
                  {/* 课程标题 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程标题</label>
                    <input
                      type="text"
                      value={getSelectedCourse()?.title || ''}
                      onChange={(e) => updateCourseTitle(selectedCourseId!, e.target.value)}
                      className="course-edit-input"
                      placeholder="请输入课程标题"
                    />
                  </div>

                  {/* 课程描述 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程描述</label>
                    <textarea
                      className="course-edit-textarea"
                      placeholder="请输入课程描述"
                      rows={4}
                      value={getSelectedCourse()?.description || ''}
                    />
                  </div>

                  {/* 课程状态 */}
                  <div className="course-edit-field">
                    <label className="course-edit-label">课程状态</label>
                    <span className="course-status-label">
                      {getSelectedCourse()?.statusLabel}
                    </span>
                  </div>

                  {/* 视频信息 */}
                  {getSelectedCourse()?.hasVideo === 1 && (
                    <div className="course-edit-field">
                      <label className="course-edit-label">视频时长</label>
                      <span className="course-video-duration">
                        {getSelectedCourse()?.videoDurationLabel}
                      </span>
                    </div>
                  )}
                </div>
              </>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="course-list-footer">
          <div className="course-list-footer-left">
            <button onClick={handlePublish} className="course-list-btn course-list-btn-publish">
              发布系列课程
            </button>
          </div>
          <div className="course-list-footer-right">
            <button onClick={handleExitEdit} className="course-list-btn course-list-btn-exit">
              退出编辑模式
            </button>
            <button onClick={handleSave} className="course-list-btn course-list-btn-save">
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourseListEditModal;
