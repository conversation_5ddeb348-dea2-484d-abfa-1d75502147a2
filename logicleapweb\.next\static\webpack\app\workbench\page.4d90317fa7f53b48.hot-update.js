"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx":
/*!**********************************************************!*\
  !*** ./app/workbench/components/CourseListEditModal.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,FileText,Plus,Settings,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Select!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _CourseListEditModal_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CourseListEditModal.css */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n// API调用函数\nconst fetchCourseList = async (seriesId)=>{\n    return await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.getSeriesCourses(seriesId);\n};\n// 获取课程标签\nconst fetchCourseTags = async ()=>{\n    return await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n        page: 1,\n        pageSize: 100,\n        status: 1 // 只获取启用的标签\n    });\n};\nconst CourseListEditModal = (param)=>{\n    let { isVisible, onClose, onSave, seriesTitle, seriesCoverImage, seriesId = 123 // 默认值，实际使用时应该传入真实的seriesId\n     } = param;\n    var _getSelectedCourse, _getSelectedCourse1, _getSelectedCourse2, _getSelectedCourse3, _getSelectedCourse4;\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [rightPanelType, setRightPanelType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"none\");\n    const [selectedCourseId, setSelectedCourseId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [editingTitle, setEditingTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(seriesTitle);\n    const [courseGoals, setCourseGoals] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseObjectives, setCourseObjectives] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [projectMembers, setProjectMembers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // 课程标签相关状态\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedTags, setSelectedTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [tagsLoading, setTagsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // 获取课程列表数据\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (isVisible && seriesId) {\n            // 检查用户登录状态\n            const token = localStorage.getItem(\"token\");\n            console.log(\"\\uD83D\\uDD10 检查登录状态，token存在:\", !!token);\n            console.log(\"\\uD83D\\uDD0D seriesId:\", seriesId);\n            if (!token) {\n                console.error(\"❌ 用户未登录，无法获取课程列表\");\n                // 设置空列表，显示空状态\n                setCourseList([]);\n                setLoading(false);\n                return;\n            }\n            loadCourseList();\n            loadCourseTags();\n        }\n    }, [\n        isVisible,\n        seriesId\n    ]);\n    const loadCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDD0D 开始加载课程列表，seriesId:\", seriesId);\n            const response = await fetchCourseList(seriesId);\n            console.log(\"\\uD83D\\uDCE1 API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程列表数据:\", response.data);\n                const courses = response.data.courses || response.data.list || [];\n                console.log(\"✅ 解析的课程数组:\", courses);\n                setCourseList(courses);\n            } else {\n                console.error(\"❌ API返回错误:\", response);\n                setCourseList([]);\n            }\n        } catch (error) {\n            var _error_response, _error_response1, _error_response2;\n            console.error(\"❌ 加载课程列表失败:\", error);\n            // 检查是否是认证错误\n            if (((_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) === 401) {\n                console.error(\"\\uD83D\\uDD10 认证失败，用户未登录或token已过期\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 403) {\n                console.error(\"\\uD83D\\uDEAB 权限不足，无法访问该系列课程\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 404) {\n                console.error(\"\\uD83D\\uDCED 系列课程不存在，seriesId:\", seriesId);\n            } else {\n                console.error(\"\\uD83D\\uDD27 其他错误:\", error.message);\n            }\n            setCourseList([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 加载课程标签\n    const loadCourseTags = async ()=>{\n        try {\n            setTagsLoading(true);\n            console.log(\"\\uD83C\\uDFF7️ 开始加载课程标签\");\n            const response = await fetchCourseTags();\n            console.log(\"\\uD83D\\uDCE1 标签API响应:\", response);\n            if (response.code === 200) {\n                console.log(\"✅ 课程标签数据:\", response.data);\n                setCourseTags(response.data || []);\n            } else {\n                console.error(\"❌ 标签API返回错误:\", response);\n                setCourseTags([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 加载课程标签失败:\", error);\n            setCourseTags([]);\n        } finally{\n            setTagsLoading(false);\n        }\n    };\n    // 添加新课程\n    const addNewCourse = ()=>{\n        const newCourse = {\n            id: Date.now(),\n            seriesId: seriesId,\n            title: \"第\".concat(courseList.length + 1, \"课 - 新课时\"),\n            description: \"\",\n            coverImage: \"\",\n            orderIndex: courseList.length + 1,\n            status: 0,\n            statusLabel: \"草稿\",\n            hasVideo: 0,\n            hasDocument: 0,\n            hasAudio: 0,\n            videoDuration: 0,\n            videoDurationLabel: \"\",\n            videoName: \"\",\n            firstTeachingTitle: \"\",\n            resourcesCount: 0,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString()\n        };\n        setCourseList([\n            ...courseList,\n            newCourse\n        ]);\n        // 自动选中新添加的课程\n        showCoursePanel(newCourse.id);\n    };\n    // 删除课程\n    const deleteCourse = (id)=>{\n        setCourseList(courseList.filter((course)=>course.id !== id));\n    };\n    // 更新课程标题\n    const updateCourseTitle = (id, newTitle)=>{\n        setCourseList(courseList.map((course)=>course.id === id ? {\n                ...course,\n                title: newTitle\n            } : course));\n    };\n    // 保存课程列表\n    const handleSave = ()=>{\n        const data = {\n            title: editingTitle,\n            courseGoals,\n            courseObjectives,\n            courseList\n        };\n        onSave(data);\n        onClose();\n    };\n    // 发布系列课程\n    const handlePublish = ()=>{\n        // TODO: 实现发布逻辑\n        alert(\"发布系列课程功能待实现\");\n    };\n    // 退出编辑模式\n    const handleExitEdit = ()=>{\n        onClose();\n    };\n    // 显示设置面板\n    const showSettingsPanel = ()=>{\n        setRightPanelType(\"settings\");\n        setSelectedCourseId(null);\n    };\n    // 显示课程编辑面板\n    const showCoursePanel = (courseId)=>{\n        setRightPanelType(\"course\");\n        setSelectedCourseId(courseId);\n    };\n    // 获取选中的课程\n    const getSelectedCourse = ()=>{\n        return courseList.find((course)=>course.id === selectedCourseId);\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"course-list-modal-overlay\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"course-list-modal\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-header\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-title-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"course-list-title\",\n                                    children: \"课程列表\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-actions\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: showSettingsPanel,\n                                            className: \"course-list-settings-btn \".concat(rightPanelType === \"settings\" ? \"active\" : \"\"),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 273,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-add-btn\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 279,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 272,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"course-list-close-btn\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                className: \"w-6 h-6\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-sidebar\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"course-list-items\",\n                                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-loading\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 295,\n                                    columnNumber: 17\n                                }, undefined) : courseList.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-list-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-list-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"w-12 h-12 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-list-empty-title\",\n                                            children: \"暂无课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-list-empty-description\",\n                                            children: \"点击右上角的 + 按钮添加第一个课时\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: addNewCourse,\n                                            className: \"course-list-empty-btn\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 311,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"添加课时\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 17\n                                }, undefined) : courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"course-list-item \".concat(selectedCourseId === course.id ? \"active\" : \"\"),\n                                        onClick: ()=>showCoursePanel(course.id),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"course-list-item-text\",\n                                                children: course.title\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: (e)=>{\n                                                    e.stopPropagation();\n                                                    deleteCourse(course.id);\n                                                },\n                                                className: \"course-list-item-delete\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"w-3 h-3\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 323,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-edit-area\",\n                            children: [\n                                rightPanelType === \"none\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-edit-empty\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-empty-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_FileText_Plus_Settings_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"w-16 h-16 text-gray-300\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"course-edit-empty-title\",\n                                            children: \"无课程详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"course-edit-empty-description\",\n                                            children: \"点击左侧课程或设置按钮查看详情\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, undefined),\n                                rightPanelType === \"settings\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: seriesCoverImage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: seriesCoverImage,\n                                                alt: \"系列课程封面\",\n                                                className: \"course-series-cover-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 21\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"系列课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 364,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 363,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 373,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: editingTitle,\n                                                            onChange: (e)=>setEditingTitle(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入系列课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标签\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Select_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            mode: \"multiple\",\n                                                            style: {\n                                                                width: \"100%\"\n                                                            },\n                                                            placeholder: \"请选择课程标签\",\n                                                            value: selectedTags,\n                                                            onChange: setSelectedTags,\n                                                            loading: tagsLoading,\n                                                            options: courseTags.map((tag)=>({\n                                                                    label: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        style: {\n                                                                            color: tag.color\n                                                                        },\n                                                                        children: tag.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                                        lineNumber: 395,\n                                                                        columnNumber: 27\n                                                                    }, void 0),\n                                                                    value: tag.id\n                                                                }))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 384,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程项目成员\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 406,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: projectMembers,\n                                                            onChange: (e)=>setProjectMembers(e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入项目成员，如：张老师、李助教、王同学\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true),\n                                rightPanelType === \"course\" && getSelectedCourse() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-series-cover\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"course-series-cover-placeholder\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"课程封面\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                lineNumber: 423,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 422,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"course-edit-form\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            value: ((_getSelectedCourse = getSelectedCourse()) === null || _getSelectedCourse === void 0 ? void 0 : _getSelectedCourse.title) || \"\",\n                                                            onChange: (e)=>updateCourseTitle(selectedCourseId, e.target.value),\n                                                            className: \"course-edit-input\",\n                                                            placeholder: \"请输入课程标题\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程描述\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                            className: \"course-edit-textarea\",\n                                                            placeholder: \"请输入课程描述\",\n                                                            rows: 4,\n                                                            value: ((_getSelectedCourse1 = getSelectedCourse()) === null || _getSelectedCourse1 === void 0 ? void 0 : _getSelectedCourse1.description) || \"\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 445,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"课程状态\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-status-label\",\n                                                            children: (_getSelectedCourse2 = getSelectedCourse()) === null || _getSelectedCourse2 === void 0 ? void 0 : _getSelectedCourse2.statusLabel\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 454,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                ((_getSelectedCourse3 = getSelectedCourse()) === null || _getSelectedCourse3 === void 0 ? void 0 : _getSelectedCourse3.hasVideo) === 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"course-edit-field\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"course-edit-label\",\n                                                            children: \"视频时长\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"course-video-duration\",\n                                                            children: (_getSelectedCourse4 = getSelectedCourse()) === null || _getSelectedCourse4 === void 0 ? void 0 : _getSelectedCourse4.videoDurationLabel\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                                    lineNumber: 463,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 339,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"course-list-footer\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-left\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handlePublish,\n                                className: \"course-list-btn course-list-btn-publish\",\n                                children: \"发布系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 478,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-list-footer-right\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleExitEdit,\n                                    className: \"course-list-btn course-list-btn-exit\",\n                                    children: \"退出编辑模式\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 484,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleSave,\n                                    className: \"course-list-btn course-list-btn-save\",\n                                    children: \"保存\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                                    lineNumber: 487,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                            lineNumber: 483,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n                    lineNumber: 477,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseListEditModal.tsx\",\n        lineNumber: 266,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseListEditModal, \"TidMEdKndIYqqYndzVsdRH7X8YQ=\");\n_c = CourseListEditModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseListEditModal);\nvar _c;\n$RefreshReg$(_c, \"CourseListEditModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9Db3Vyc2VMaXN0RWRpdE1vZGFsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFFbUQ7QUFDa0I7QUFDSDtBQUNyQjtBQUNmO0FBQ0s7QUF5RG5DLFVBQVU7QUFDVixNQUFNVyxrQkFBa0IsT0FBT0M7SUFDN0IsT0FBTyxNQUFNSiwyRUFBbUJBLENBQUNLLGdCQUFnQixDQUFDRDtBQUNwRDtBQUVBLFNBQVM7QUFDVCxNQUFNRSxrQkFBa0I7SUFDdEIsT0FBTyxNQUFNTCxzREFBU0EsQ0FBQ00sYUFBYSxDQUFDO1FBQ25DQyxNQUFNO1FBQ05DLFVBQVU7UUFDVkMsUUFBUSxFQUFFLFdBQVc7SUFDdkI7QUFDRjtBQUVBLE1BQU1DLHNCQUEwRDtRQUFDLEVBQy9EQyxTQUFTLEVBQ1RDLE9BQU8sRUFDUEMsTUFBTSxFQUNOQyxXQUFXLEVBQ1hDLGdCQUFnQixFQUNoQlosV0FBVyxJQUFJLDJCQUEyQjtJQUE1QixFQUNmO1FBNlY0QmEsb0JBY0FBLHFCQVFOQSxxQkFLSkEscUJBSU1BOztJQTNYdkIsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUcxQiwrQ0FBUUEsQ0FBZSxFQUFFO0lBQzdELE1BQU0sQ0FBQzJCLFNBQVNDLFdBQVcsR0FBRzVCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQzZCLGdCQUFnQkMsa0JBQWtCLEdBQUc5QiwrQ0FBUUEsQ0FBaUM7SUFDckYsTUFBTSxDQUFDK0Isa0JBQWtCQyxvQkFBb0IsR0FBR2hDLCtDQUFRQSxDQUFnQjtJQUV4RSxNQUFNLENBQUNpQyxjQUFjQyxnQkFBZ0IsR0FBR2xDLCtDQUFRQSxDQUFDc0I7SUFDakQsTUFBTSxDQUFDYSxhQUFhQyxlQUFlLEdBQUdwQywrQ0FBUUEsQ0FBQztJQUMvQyxNQUFNLENBQUNxQyxrQkFBa0JDLG9CQUFvQixHQUFHdEMsK0NBQVFBLENBQUM7SUFDekQsTUFBTSxDQUFDdUMsZ0JBQWdCQyxrQkFBa0IsR0FBR3hDLCtDQUFRQSxDQUFDO0lBRXJELFdBQVc7SUFDWCxNQUFNLENBQUN5QyxZQUFZQyxjQUFjLEdBQUcxQywrQ0FBUUEsQ0FBYyxFQUFFO0lBQzVELE1BQU0sQ0FBQzJDLGNBQWNDLGdCQUFnQixHQUFHNUMsK0NBQVFBLENBQVcsRUFBRTtJQUM3RCxNQUFNLENBQUM2QyxhQUFhQyxlQUFlLEdBQUc5QywrQ0FBUUEsQ0FBQztJQUUvQyxXQUFXO0lBQ1hDLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSWtCLGFBQWFSLFVBQVU7WUFDekIsV0FBVztZQUNYLE1BQU1vQyxRQUFRQyxhQUFhQyxPQUFPLENBQUM7WUFDbkNDLFFBQVFDLEdBQUcsQ0FBQyxnQ0FBc0IsQ0FBQyxDQUFDSjtZQUNwQ0csUUFBUUMsR0FBRyxDQUFDLDBCQUFnQnhDO1lBRTVCLElBQUksQ0FBQ29DLE9BQU87Z0JBQ1ZHLFFBQVFFLEtBQUssQ0FBQztnQkFDZCxjQUFjO2dCQUNkMUIsY0FBYyxFQUFFO2dCQUNoQkUsV0FBVztnQkFDWDtZQUNGO1lBRUF5QjtZQUNBQztRQUNGO0lBQ0YsR0FBRztRQUFDbkM7UUFBV1I7S0FBUztJQUV4QixNQUFNMEMsaUJBQWlCO1FBQ3JCLElBQUk7WUFDRnpCLFdBQVc7WUFDWHNCLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBeUJ4QztZQUVyQyxNQUFNNEMsV0FBVyxNQUFNN0MsZ0JBQWdCQztZQUN2Q3VDLFFBQVFDLEdBQUcsQ0FBQyx1QkFBYUk7WUFFekIsSUFBSUEsU0FBU0MsSUFBSSxLQUFLLEtBQUs7Z0JBQ3pCTixRQUFRQyxHQUFHLENBQUMsYUFBYUksU0FBU0UsSUFBSTtnQkFDdEMsTUFBTUMsVUFBVUgsU0FBU0UsSUFBSSxDQUFDQyxPQUFPLElBQUlILFNBQVNFLElBQUksQ0FBQ0UsSUFBSSxJQUFJLEVBQUU7Z0JBQ2pFVCxRQUFRQyxHQUFHLENBQUMsY0FBY087Z0JBQzFCaEMsY0FBY2dDO1lBQ2hCLE9BQU87Z0JBQ0xSLFFBQVFFLEtBQUssQ0FBQyxjQUFjRztnQkFDNUI3QixjQUFjLEVBQUU7WUFDbEI7UUFDRixFQUFFLE9BQU8wQixPQUFZO2dCQUlmQSxpQkFFT0Esa0JBRUFBO1lBUFhGLFFBQVFFLEtBQUssQ0FBQyxlQUFlQTtZQUU3QixZQUFZO1lBQ1osSUFBSUEsRUFBQUEsa0JBQUFBLE1BQU1HLFFBQVEsY0FBZEgsc0NBQUFBLGdCQUFnQm5DLE1BQU0sTUFBSyxLQUFLO2dCQUNsQ2lDLFFBQVFFLEtBQUssQ0FBQztZQUNoQixPQUFPLElBQUlBLEVBQUFBLG1CQUFBQSxNQUFNRyxRQUFRLGNBQWRILHVDQUFBQSxpQkFBZ0JuQyxNQUFNLE1BQUssS0FBSztnQkFDekNpQyxRQUFRRSxLQUFLLENBQUM7WUFDaEIsT0FBTyxJQUFJQSxFQUFBQSxtQkFBQUEsTUFBTUcsUUFBUSxjQUFkSCx1Q0FBQUEsaUJBQWdCbkMsTUFBTSxNQUFLLEtBQUs7Z0JBQ3pDaUMsUUFBUUUsS0FBSyxDQUFDLGtDQUF3QnpDO1lBQ3hDLE9BQU87Z0JBQ0x1QyxRQUFRRSxLQUFLLENBQUMsc0JBQVlBLE1BQU1RLE9BQU87WUFDekM7WUFFQWxDLGNBQWMsRUFBRTtRQUNsQixTQUFVO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsU0FBUztJQUNULE1BQU0wQixpQkFBaUI7UUFDckIsSUFBSTtZQUNGUixlQUFlO1lBQ2ZJLFFBQVFDLEdBQUcsQ0FBQztZQUVaLE1BQU1JLFdBQVcsTUFBTTFDO1lBQ3ZCcUMsUUFBUUMsR0FBRyxDQUFDLHlCQUFlSTtZQUUzQixJQUFJQSxTQUFTQyxJQUFJLEtBQUssS0FBSztnQkFDekJOLFFBQVFDLEdBQUcsQ0FBQyxhQUFhSSxTQUFTRSxJQUFJO2dCQUN0Q2YsY0FBY2EsU0FBU0UsSUFBSSxJQUFJLEVBQUU7WUFDbkMsT0FBTztnQkFDTFAsUUFBUUUsS0FBSyxDQUFDLGdCQUFnQkc7Z0JBQzlCYixjQUFjLEVBQUU7WUFDbEI7UUFDRixFQUFFLE9BQU9VLE9BQVk7WUFDbkJGLFFBQVFFLEtBQUssQ0FBQyxlQUFlQTtZQUM3QlYsY0FBYyxFQUFFO1FBQ2xCLFNBQVU7WUFDUkksZUFBZTtRQUNqQjtJQUNGO0lBRUEsUUFBUTtJQUNSLE1BQU1lLGVBQWU7UUFDbkIsTUFBTUMsWUFBd0I7WUFDNUJDLElBQUlDLEtBQUtDLEdBQUc7WUFDWnRELFVBQVVBO1lBQ1Z1RCxPQUFPLElBQTBCLE9BQXRCekMsV0FBVzBDLE1BQU0sR0FBRyxHQUFFO1lBQ2pDQyxhQUFhO1lBQ2JDLFlBQVk7WUFDWkMsWUFBWTdDLFdBQVcwQyxNQUFNLEdBQUc7WUFDaENsRCxRQUFRO1lBQ1JzRCxhQUFhO1lBQ2JDLFVBQVU7WUFDVkMsYUFBYTtZQUNiQyxVQUFVO1lBQ1ZDLGVBQWU7WUFDZkMsb0JBQW9CO1lBQ3BCQyxXQUFXO1lBQ1hDLG9CQUFvQjtZQUNwQkMsZ0JBQWdCO1lBQ2hCQyxXQUFXLElBQUloQixPQUFPaUIsV0FBVztZQUNqQ0MsV0FBVyxJQUFJbEIsT0FBT2lCLFdBQVc7UUFDbkM7UUFDQXZELGNBQWM7ZUFBSUQ7WUFBWXFDO1NBQVU7UUFDeEMsYUFBYTtRQUNicUIsZ0JBQWdCckIsVUFBVUMsRUFBRTtJQUM5QjtJQUVBLE9BQU87SUFDUCxNQUFNcUIsZUFBZSxDQUFDckI7UUFDcEJyQyxjQUFjRCxXQUFXNEQsTUFBTSxDQUFDQyxDQUFBQSxTQUFVQSxPQUFPdkIsRUFBRSxLQUFLQTtJQUMxRDtJQUVBLFNBQVM7SUFDVCxNQUFNd0Isb0JBQW9CLENBQUN4QixJQUFZeUI7UUFDckM5RCxjQUFjRCxXQUFXZ0UsR0FBRyxDQUFDSCxDQUFBQSxTQUMzQkEsT0FBT3ZCLEVBQUUsS0FBS0EsS0FBSztnQkFBRSxHQUFHdUIsTUFBTTtnQkFBRXBCLE9BQU9zQjtZQUFTLElBQUlGO0lBRXhEO0lBRUEsU0FBUztJQUNULE1BQU1JLGFBQWE7UUFDakIsTUFBTWpDLE9BQU87WUFDWFMsT0FBT2pDO1lBQ1BFO1lBQ0FFO1lBQ0FaO1FBQ0Y7UUFDQUosT0FBT29DO1FBQ1ByQztJQUNGO0lBRUEsU0FBUztJQUNULE1BQU11RSxnQkFBZ0I7UUFDcEIsZUFBZTtRQUNmQyxNQUFNO0lBQ1I7SUFFQSxTQUFTO0lBQ1QsTUFBTUMsaUJBQWlCO1FBQ3JCekU7SUFDRjtJQUVBLFNBQVM7SUFDVCxNQUFNMEUsb0JBQW9CO1FBQ3hCaEUsa0JBQWtCO1FBQ2xCRSxvQkFBb0I7SUFDdEI7SUFFQSxXQUFXO0lBQ1gsTUFBTW1ELGtCQUFrQixDQUFDWTtRQUN2QmpFLGtCQUFrQjtRQUNsQkUsb0JBQW9CK0Q7SUFDdEI7SUFFQSxVQUFVO0lBQ1YsTUFBTXZFLG9CQUFvQjtRQUN4QixPQUFPQyxXQUFXdUUsSUFBSSxDQUFDVixDQUFBQSxTQUFVQSxPQUFPdkIsRUFBRSxLQUFLaEM7SUFDakQ7SUFFQSxJQUFJLENBQUNaLFdBQVcsT0FBTztJQUV2QixxQkFDRSw4REFBQzhFO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7OzhCQUViLDhEQUFDRDtvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUNEOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUdELFdBQVU7OENBQW9COzs7Ozs7OENBQ2xDLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNFOzRDQUNDQyxTQUFTUDs0Q0FDVEksV0FBVyw0QkFBMEUsT0FBOUNyRSxtQkFBbUIsYUFBYSxXQUFXO3NEQUVsRiw0RUFBQzFCLDZHQUFRQTtnREFBQytGLFdBQVU7Ozs7Ozs7Ozs7O3NEQUV0Qiw4REFBQ0U7NENBQU9DLFNBQVN4Qzs0Q0FBY3FDLFdBQVU7c0RBQ3ZDLDRFQUFDOUYsNkdBQUlBO2dEQUFDOEYsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSXRCLDhEQUFDRTs0QkFBT0MsU0FBU2pGOzRCQUFTOEUsV0FBVTtzQ0FDbEMsNEVBQUNoRyw2R0FBQ0E7Z0NBQUNnRyxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFLakIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNEO2dDQUFJQyxXQUFVOzBDQUNadkUsd0JBQ0MsOERBQUNzRTtvQ0FBSUMsV0FBVTs4Q0FDYiw0RUFBQ0k7a0RBQUU7Ozs7Ozs7Ozs7Z0RBRUg3RSxXQUFXMEMsTUFBTSxLQUFLLGtCQUN4Qiw4REFBQzhCO29DQUFJQyxXQUFVOztzREFDYiw4REFBQ0Q7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUM3Riw2R0FBUUE7Z0RBQUM2RixXQUFVOzs7Ozs7Ozs7OztzREFFdEIsOERBQUNLOzRDQUFHTCxXQUFVO3NEQUEwQjs7Ozs7O3NEQUN4Qyw4REFBQ0k7NENBQUVKLFdBQVU7c0RBQWdDOzs7Ozs7c0RBRzdDLDhEQUFDRTs0Q0FDQ0MsU0FBU3hDOzRDQUNUcUMsV0FBVTs7OERBRVYsOERBQUM5Riw2R0FBSUE7b0RBQUM4RixXQUFVOzs7Ozs7Z0RBQVk7Ozs7Ozs7Ozs7OztnREFLaEN6RSxXQUFXZ0UsR0FBRyxDQUFDLENBQUNILHVCQUNkLDhEQUFDVzt3Q0FFQ0MsV0FBVyxvQkFBbUUsT0FBL0NuRSxxQkFBcUJ1RCxPQUFPdkIsRUFBRSxHQUFHLFdBQVc7d0NBQzNFc0MsU0FBUyxJQUFNbEIsZ0JBQWdCRyxPQUFPdkIsRUFBRTs7MERBRXhDLDhEQUFDeUM7Z0RBQUtOLFdBQVU7MERBQXlCWixPQUFPcEIsS0FBSzs7Ozs7OzBEQUNyRCw4REFBQ2tDO2dEQUNDQyxTQUFTLENBQUNJO29EQUNSQSxFQUFFQyxlQUFlO29EQUNqQnRCLGFBQWFFLE9BQU92QixFQUFFO2dEQUN4QjtnREFDQW1DLFdBQVU7MERBRVYsNEVBQUNoRyw2R0FBQ0E7b0RBQUNnRyxXQUFVOzs7Ozs7Ozs7Ozs7dUNBWlZaLE9BQU92QixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7c0NBcUJ4Qiw4REFBQ2tDOzRCQUFJQyxXQUFVOztnQ0FDWnJFLG1CQUFtQix3QkFDbEIsOERBQUNvRTtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUNEOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDNUYsNkdBQVFBO2dEQUFDNEYsV0FBVTs7Ozs7Ozs7Ozs7c0RBRXRCLDhEQUFDSzs0Q0FBR0wsV0FBVTtzREFBMEI7Ozs7OztzREFDeEMsOERBQUNJOzRDQUFFSixXQUFVO3NEQUFnQzs7Ozs7Ozs7Ozs7O2dDQU1oRHJFLG1CQUFtQiw0QkFDbEI7O3NEQUVFLDhEQUFDb0U7NENBQUlDLFdBQVU7c0RBQ1ozRSxpQ0FDQyw4REFBQ29GO2dEQUNDQyxLQUFLckY7Z0RBQ0xzRixLQUFJO2dEQUNKWCxXQUFVOzs7OzswRUFHWiw4REFBQ0Q7Z0RBQUlDLFdBQVU7MERBQ2IsNEVBQUNNOzhEQUFLOzs7Ozs7Ozs7Ozs7Ozs7O3NEQU1aLDhEQUFDUDs0Q0FBSUMsV0FBVTs7OERBRWIsOERBQUNEO29EQUFJQyxXQUFVOztzRUFDYiw4REFBQ1k7NERBQU1aLFdBQVU7c0VBQW9COzs7Ozs7c0VBQ3JDLDhEQUFDYTs0REFDQ0MsTUFBSzs0REFDTEMsT0FBT2hGOzREQUNQaUYsVUFBVSxDQUFDVCxJQUFNdkUsZ0JBQWdCdUUsRUFBRVUsTUFBTSxDQUFDRixLQUFLOzREQUMvQ2YsV0FBVTs0REFDVmtCLGFBQVk7Ozs7Ozs7Ozs7Ozs4REFLaEIsOERBQUNuQjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFNWixXQUFVO3NFQUFvQjs7Ozs7O3NFQUNyQyw4REFBQ3pGLDJFQUFNQTs0REFDTDRHLE1BQUs7NERBQ0xDLE9BQU87Z0VBQUVDLE9BQU87NERBQU87NERBQ3ZCSCxhQUFZOzREQUNaSCxPQUFPdEU7NERBQ1B1RSxVQUFVdEU7NERBQ1ZqQixTQUFTa0I7NERBQ1QyRSxTQUFTL0UsV0FBV2dELEdBQUcsQ0FBQ2dDLENBQUFBLE1BQVE7b0VBQzlCWCxxQkFDRSw4REFBQ047d0VBQUtjLE9BQU87NEVBQUVJLE9BQU9ELElBQUlDLEtBQUs7d0VBQUM7a0ZBQzdCRCxJQUFJRSxJQUFJOzs7Ozs7b0VBR2JWLE9BQU9RLElBQUkxRCxFQUFFO2dFQUNmOzs7Ozs7Ozs7Ozs7OERBS0osOERBQUNrQztvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFNWixXQUFVO3NFQUFvQjs7Ozs7O3NFQUNyQyw4REFBQ2E7NERBQ0NDLE1BQUs7NERBQ0xDLE9BQU8xRTs0REFDUDJFLFVBQVUsQ0FBQ1QsSUFBTWpFLGtCQUFrQmlFLEVBQUVVLE1BQU0sQ0FBQ0YsS0FBSzs0REFDakRmLFdBQVU7NERBQ1ZrQixhQUFZOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztnQ0FPckJ2RixtQkFBbUIsWUFBWUwscUNBQzlCOztzREFFRSw4REFBQ3lFOzRDQUFJQyxXQUFVO3NEQUNiLDRFQUFDRDtnREFBSUMsV0FBVTswREFDYiw0RUFBQ007OERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7c0RBS1YsOERBQUNQOzRDQUFJQyxXQUFVOzs4REFFYiw4REFBQ0Q7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDWTs0REFBTVosV0FBVTtzRUFBb0I7Ozs7OztzRUFDckMsOERBQUNhOzREQUNDQyxNQUFLOzREQUNMQyxPQUFPekYsRUFBQUEscUJBQUFBLGlDQUFBQSx5Q0FBQUEsbUJBQXFCMEMsS0FBSyxLQUFJOzREQUNyQ2dELFVBQVUsQ0FBQ1QsSUFBTWxCLGtCQUFrQnhELGtCQUFtQjBFLEVBQUVVLE1BQU0sQ0FBQ0YsS0FBSzs0REFDcEVmLFdBQVU7NERBQ1ZrQixhQUFZOzs7Ozs7Ozs7Ozs7OERBS2hCLDhEQUFDbkI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDWTs0REFBTVosV0FBVTtzRUFBb0I7Ozs7OztzRUFDckMsOERBQUMwQjs0REFDQzFCLFdBQVU7NERBQ1ZrQixhQUFZOzREQUNaUyxNQUFNOzREQUNOWixPQUFPekYsRUFBQUEsc0JBQUFBLGlDQUFBQSwwQ0FBQUEsb0JBQXFCNEMsV0FBVyxLQUFJOzs7Ozs7Ozs7Ozs7OERBSy9DLDhEQUFDNkI7b0RBQUlDLFdBQVU7O3NFQUNiLDhEQUFDWTs0REFBTVosV0FBVTtzRUFBb0I7Ozs7OztzRUFDckMsOERBQUNNOzREQUFLTixXQUFVO3VFQUNiMUUsc0JBQUFBLGlDQUFBQSwwQ0FBQUEsb0JBQXFCK0MsV0FBVzs7Ozs7Ozs7Ozs7O2dEQUtwQy9DLEVBQUFBLHNCQUFBQSxpQ0FBQUEsMENBQUFBLG9CQUFxQmdELFFBQVEsTUFBSyxtQkFDakMsOERBQUN5QjtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUNZOzREQUFNWixXQUFVO3NFQUFvQjs7Ozs7O3NFQUNyQyw4REFBQ007NERBQUtOLFdBQVU7dUVBQ2IxRSxzQkFBQUEsaUNBQUFBLDBDQUFBQSxvQkFBcUJvRCxrQkFBa0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVd4RCw4REFBQ3FCO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUNFO2dDQUFPQyxTQUFTVjtnQ0FBZU8sV0FBVTswQ0FBMEM7Ozs7Ozs7Ozs7O3NDQUl0Riw4REFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDRTtvQ0FBT0MsU0FBU1I7b0NBQWdCSyxXQUFVOzhDQUF1Qzs7Ozs7OzhDQUdsRiw4REFBQ0U7b0NBQU9DLFNBQVNYO29DQUFZUSxXQUFVOzhDQUF1Qzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFRMUY7R0FoYU1oRjtLQUFBQTtBQWthTiwrREFBZUEsbUJBQW1CQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL2FwcC93b3JrYmVuY2gvY29tcG9uZW50cy9Db3Vyc2VMaXN0RWRpdE1vZGFsLnRzeD8yZDhmIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcblxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBYLCBTZXR0aW5ncywgUGx1cywgQm9va09wZW4sIEZpbGVUZXh0IH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcbmltcG9ydCB7IGNvdXJzZU1hbmFnZW1lbnRBcGkgfSBmcm9tICdAL2xpYi9hcGkvY291cnNlLW1hbmFnZW1lbnQnO1xuaW1wb3J0IHsgY291cnNlQXBpIH0gZnJvbSAnQC9saWIvYXBpL2NvdXJzZSc7XG5pbXBvcnQgeyBTZWxlY3QgfSBmcm9tICdhbnRkJztcbmltcG9ydCAnLi9Db3Vyc2VMaXN0RWRpdE1vZGFsLmNzcyc7XG5cbmludGVyZmFjZSBDb3Vyc2VJdGVtIHtcbiAgaWQ6IG51bWJlcjtcbiAgc2VyaWVzSWQ6IG51bWJlcjtcbiAgdGl0bGU6IHN0cmluZztcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgY292ZXJJbWFnZTogc3RyaW5nO1xuICBvcmRlckluZGV4OiBudW1iZXI7XG4gIHN0YXR1czogbnVtYmVyO1xuICBzdGF0dXNMYWJlbDogc3RyaW5nO1xuICBoYXNWaWRlbzogbnVtYmVyO1xuICBoYXNEb2N1bWVudDogbnVtYmVyO1xuICBoYXNBdWRpbzogbnVtYmVyO1xuICB2aWRlb0R1cmF0aW9uOiBudW1iZXI7XG4gIHZpZGVvRHVyYXRpb25MYWJlbDogc3RyaW5nO1xuICB2aWRlb05hbWU6IHN0cmluZztcbiAgZmlyc3RUZWFjaGluZ1RpdGxlOiBzdHJpbmc7XG4gIHJlc291cmNlc0NvdW50OiBudW1iZXI7XG4gIGNyZWF0ZWRBdDogc3RyaW5nO1xuICB1cGRhdGVkQXQ6IHN0cmluZztcbn1cblxuaW50ZXJmYWNlIENvdXJzZVRhZyB7XG4gIGlkOiBudW1iZXI7XG4gIG5hbWU6IHN0cmluZztcbiAgY29sb3I6IHN0cmluZztcbiAgY2F0ZWdvcnk6IG51bWJlcjtcbiAgZGVzY3JpcHRpb246IHN0cmluZztcbiAgc3RhdHVzOiBudW1iZXI7XG59XG5cbmludGVyZmFjZSBBcGlSZXNwb25zZSB7XG4gIGNvZGU6IG51bWJlcjtcbiAgbWVzc2FnZTogc3RyaW5nO1xuICBkYXRhOiB7XG4gICAgbGlzdDogQ291cnNlSXRlbVtdO1xuICAgIHBhZ2luYXRpb246IHtcbiAgICAgIHBhZ2U6IG51bWJlcjtcbiAgICAgIHBhZ2VTaXplOiBudW1iZXI7XG4gICAgICB0b3RhbDogbnVtYmVyO1xuICAgICAgdG90YWxQYWdlczogbnVtYmVyO1xuICAgICAgaGFzTmV4dDogYm9vbGVhbjtcbiAgICAgIGhhc1ByZXY6IGJvb2xlYW47XG4gICAgfTtcbiAgfTtcbn1cblxuaW50ZXJmYWNlIENvdXJzZUxpc3RFZGl0TW9kYWxQcm9wcyB7XG4gIGlzVmlzaWJsZTogYm9vbGVhbjtcbiAgb25DbG9zZTogKCkgPT4gdm9pZDtcbiAgb25TYXZlOiAoZGF0YTogYW55KSA9PiB2b2lkO1xuICBzZXJpZXNUaXRsZTogc3RyaW5nO1xuICBzZXJpZXNDb3ZlckltYWdlPzogc3RyaW5nO1xuICBzZXJpZXNJZDogbnVtYmVyO1xufVxuXG4vLyBBUEnosIPnlKjlh73mlbBcbmNvbnN0IGZldGNoQ291cnNlTGlzdCA9IGFzeW5jIChzZXJpZXNJZDogbnVtYmVyKTogUHJvbWlzZTxBcGlSZXNwb25zZT4gPT4ge1xuICByZXR1cm4gYXdhaXQgY291cnNlTWFuYWdlbWVudEFwaS5nZXRTZXJpZXNDb3Vyc2VzKHNlcmllc0lkKTtcbn07XG5cbi8vIOiOt+WPluivvueoi+agh+etvlxuY29uc3QgZmV0Y2hDb3Vyc2VUYWdzID0gYXN5bmMgKCk6IFByb21pc2U8eyBjb2RlOiBudW1iZXI7IGRhdGE6IHsgbGlzdDogQ291cnNlVGFnW10gfSB9PiA9PiB7XG4gIHJldHVybiBhd2FpdCBjb3Vyc2VBcGkuZ2V0Q291cnNlVGFncyh7XG4gICAgcGFnZTogMSxcbiAgICBwYWdlU2l6ZTogMTAwLFxuICAgIHN0YXR1czogMSAvLyDlj6rojrflj5blkK/nlKjnmoTmoIfnrb5cbiAgfSk7XG59O1xuXG5jb25zdCBDb3Vyc2VMaXN0RWRpdE1vZGFsOiBSZWFjdC5GQzxDb3Vyc2VMaXN0RWRpdE1vZGFsUHJvcHM+ID0gKHtcbiAgaXNWaXNpYmxlLFxuICBvbkNsb3NlLFxuICBvblNhdmUsXG4gIHNlcmllc1RpdGxlLFxuICBzZXJpZXNDb3ZlckltYWdlLFxuICBzZXJpZXNJZCA9IDEyMyAvLyDpu5jorqTlgLzvvIzlrp7pmYXkvb/nlKjml7blupTor6XkvKDlhaXnnJ/lrp7nmoRzZXJpZXNJZFxufSkgPT4ge1xuICBjb25zdCBbY291cnNlTGlzdCwgc2V0Q291cnNlTGlzdF0gPSB1c2VTdGF0ZTxDb3Vyc2VJdGVtW10+KFtdKTtcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbcmlnaHRQYW5lbFR5cGUsIHNldFJpZ2h0UGFuZWxUeXBlXSA9IHVzZVN0YXRlPCdub25lJyB8ICdzZXR0aW5ncycgfCAnY291cnNlJz4oJ25vbmUnKTtcbiAgY29uc3QgW3NlbGVjdGVkQ291cnNlSWQsIHNldFNlbGVjdGVkQ291cnNlSWRdID0gdXNlU3RhdGU8bnVtYmVyIHwgbnVsbD4obnVsbCk7XG5cbiAgY29uc3QgW2VkaXRpbmdUaXRsZSwgc2V0RWRpdGluZ1RpdGxlXSA9IHVzZVN0YXRlKHNlcmllc1RpdGxlKTtcbiAgY29uc3QgW2NvdXJzZUdvYWxzLCBzZXRDb3Vyc2VHb2Fsc10gPSB1c2VTdGF0ZSgnJyk7XG4gIGNvbnN0IFtjb3Vyc2VPYmplY3RpdmVzLCBzZXRDb3Vyc2VPYmplY3RpdmVzXSA9IHVzZVN0YXRlKCcnKTtcbiAgY29uc3QgW3Byb2plY3RNZW1iZXJzLCBzZXRQcm9qZWN0TWVtYmVyc10gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgLy8g6K++56iL5qCH562+55u45YWz54q25oCBXG4gIGNvbnN0IFtjb3Vyc2VUYWdzLCBzZXRDb3Vyc2VUYWdzXSA9IHVzZVN0YXRlPENvdXJzZVRhZ1tdPihbXSk7XG4gIGNvbnN0IFtzZWxlY3RlZFRhZ3MsIHNldFNlbGVjdGVkVGFnc10gPSB1c2VTdGF0ZTxudW1iZXJbXT4oW10pO1xuICBjb25zdCBbdGFnc0xvYWRpbmcsIHNldFRhZ3NMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICAvLyDojrflj5bor77nqIvliJfooajmlbDmja5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNWaXNpYmxlICYmIHNlcmllc0lkKSB7XG4gICAgICAvLyDmo4Dmn6XnlKjmiLfnmbvlvZXnirbmgIFcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJyk7XG4gICAgICBjb25zb2xlLmxvZygn8J+UkCDmo4Dmn6XnmbvlvZXnirbmgIHvvIx0b2tlbuWtmOWcqDonLCAhIXRva2VuKTtcbiAgICAgIGNvbnNvbGUubG9nKCfwn5SNIHNlcmllc0lkOicsIHNlcmllc0lkKTtcblxuICAgICAgaWYgKCF0b2tlbikge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwg55So5oi35pyq55m75b2V77yM5peg5rOV6I635Y+W6K++56iL5YiX6KGoJyk7XG4gICAgICAgIC8vIOiuvue9ruepuuWIl+ihqO+8jOaYvuekuuepuueKtuaAgVxuICAgICAgICBzZXRDb3Vyc2VMaXN0KFtdKTtcbiAgICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgbG9hZENvdXJzZUxpc3QoKTtcbiAgICAgIGxvYWRDb3Vyc2VUYWdzKCk7XG4gICAgfVxuICB9LCBbaXNWaXNpYmxlLCBzZXJpZXNJZF0pO1xuXG4gIGNvbnN0IGxvYWRDb3Vyc2VMaXN0ID0gYXN5bmMgKCkgPT4ge1xuICAgIHRyeSB7XG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/CflI0g5byA5aeL5Yqg6L296K++56iL5YiX6KGo77yMc2VyaWVzSWQ6Jywgc2VyaWVzSWQpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoQ291cnNlTGlzdChzZXJpZXNJZCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToSBBUEnlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5YiX6KGo5pWw5o2uOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICBjb25zdCBjb3Vyc2VzID0gcmVzcG9uc2UuZGF0YS5jb3Vyc2VzIHx8IHJlc3BvbnNlLmRhdGEubGlzdCB8fCBbXTtcbiAgICAgICAgY29uc29sZS5sb2coJ+KchSDop6PmnpDnmoTor77nqIvmlbDnu4Q6JywgY291cnNlcyk7XG4gICAgICAgIHNldENvdXJzZUxpc3QoY291cnNlcyk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfinYwgQVBJ6L+U5Zue6ZSZ6K+vOicsIHJlc3BvbnNlKTtcbiAgICAgICAgc2V0Q291cnNlTGlzdChbXSk7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcign4p2MIOWKoOi9veivvueoi+WIl+ihqOWksei0pTonLCBlcnJvcik7XG5cbiAgICAgIC8vIOajgOafpeaYr+WQpuaYr+iupOivgemUmeivr1xuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SQIOiupOivgeWksei0pe+8jOeUqOaIt+acqueZu+W9leaIlnRva2Vu5bey6L+H5pyfJyk7XG4gICAgICB9IGVsc2UgaWYgKGVycm9yLnJlc3BvbnNlPy5zdGF0dXMgPT09IDQwMykge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5qrIOadg+mZkOS4jei2s++8jOaXoOazleiuv+mXruivpeezu+WIl+ivvueoiycpO1xuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uc3RhdHVzID09PSA0MDQpIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign8J+TrSDns7vliJfor77nqIvkuI3lrZjlnKjvvIxzZXJpZXNJZDonLCBzZXJpZXNJZCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICBjb25zb2xlLmVycm9yKCfwn5SnIOWFtuS7lumUmeivrzonLCBlcnJvci5tZXNzYWdlKTtcbiAgICAgIH1cblxuICAgICAgc2V0Q291cnNlTGlzdChbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyDliqDovb3or77nqIvmoIfnrb5cbiAgY29uc3QgbG9hZENvdXJzZVRhZ3MgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldFRhZ3NMb2FkaW5nKHRydWUpO1xuICAgICAgY29uc29sZS5sb2coJ/Cfj7fvuI8g5byA5aeL5Yqg6L296K++56iL5qCH562+Jyk7XG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2hDb3Vyc2VUYWdzKCk7XG4gICAgICBjb25zb2xlLmxvZygn8J+ToSDmoIfnrb5BUEnlk43lupQ6JywgcmVzcG9uc2UpO1xuXG4gICAgICBpZiAocmVzcG9uc2UuY29kZSA9PT0gMjAwKSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCfinIUg6K++56iL5qCH562+5pWw5o2uOicsIHJlc3BvbnNlLmRhdGEpO1xuICAgICAgICBzZXRDb3Vyc2VUYWdzKHJlc3BvbnNlLmRhdGEgfHwgW10pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5lcnJvcign4p2MIOagh+etvkFQSei/lOWbnumUmeivrzonLCByZXNwb25zZSk7XG4gICAgICAgIHNldENvdXJzZVRhZ3MoW10pO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ+KdjCDliqDovb3or77nqIvmoIfnrb7lpLHotKU6JywgZXJyb3IpO1xuICAgICAgc2V0Q291cnNlVGFncyhbXSk7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldFRhZ3NMb2FkaW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgLy8g5re75Yqg5paw6K++56iLXG4gIGNvbnN0IGFkZE5ld0NvdXJzZSA9ICgpID0+IHtcbiAgICBjb25zdCBuZXdDb3Vyc2U6IENvdXJzZUl0ZW0gPSB7XG4gICAgICBpZDogRGF0ZS5ub3coKSxcbiAgICAgIHNlcmllc0lkOiBzZXJpZXNJZCxcbiAgICAgIHRpdGxlOiBg56ysJHtjb3Vyc2VMaXN0Lmxlbmd0aCArIDF96K++IC0g5paw6K++5pe2YCxcbiAgICAgIGRlc2NyaXB0aW9uOiAnJyxcbiAgICAgIGNvdmVySW1hZ2U6ICcnLFxuICAgICAgb3JkZXJJbmRleDogY291cnNlTGlzdC5sZW5ndGggKyAxLFxuICAgICAgc3RhdHVzOiAwLFxuICAgICAgc3RhdHVzTGFiZWw6ICfojYnnqL8nLFxuICAgICAgaGFzVmlkZW86IDAsXG4gICAgICBoYXNEb2N1bWVudDogMCxcbiAgICAgIGhhc0F1ZGlvOiAwLFxuICAgICAgdmlkZW9EdXJhdGlvbjogMCxcbiAgICAgIHZpZGVvRHVyYXRpb25MYWJlbDogJycsXG4gICAgICB2aWRlb05hbWU6ICcnLFxuICAgICAgZmlyc3RUZWFjaGluZ1RpdGxlOiAnJyxcbiAgICAgIHJlc291cmNlc0NvdW50OiAwLFxuICAgICAgY3JlYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXG4gICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKVxuICAgIH07XG4gICAgc2V0Q291cnNlTGlzdChbLi4uY291cnNlTGlzdCwgbmV3Q291cnNlXSk7XG4gICAgLy8g6Ieq5Yqo6YCJ5Lit5paw5re75Yqg55qE6K++56iLXG4gICAgc2hvd0NvdXJzZVBhbmVsKG5ld0NvdXJzZS5pZCk7XG4gIH07XG5cbiAgLy8g5Yig6Zmk6K++56iLXG4gIGNvbnN0IGRlbGV0ZUNvdXJzZSA9IChpZDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Q291cnNlTGlzdChjb3Vyc2VMaXN0LmZpbHRlcihjb3Vyc2UgPT4gY291cnNlLmlkICE9PSBpZCkpO1xuICB9O1xuXG4gIC8vIOabtOaWsOivvueoi+agh+mimFxuICBjb25zdCB1cGRhdGVDb3Vyc2VUaXRsZSA9IChpZDogbnVtYmVyLCBuZXdUaXRsZTogc3RyaW5nKSA9PiB7XG4gICAgc2V0Q291cnNlTGlzdChjb3Vyc2VMaXN0Lm1hcChjb3Vyc2UgPT4gXG4gICAgICBjb3Vyc2UuaWQgPT09IGlkID8geyAuLi5jb3Vyc2UsIHRpdGxlOiBuZXdUaXRsZSB9IDogY291cnNlXG4gICAgKSk7XG4gIH07XG5cbiAgLy8g5L+d5a2Y6K++56iL5YiX6KGoXG4gIGNvbnN0IGhhbmRsZVNhdmUgPSAoKSA9PiB7XG4gICAgY29uc3QgZGF0YSA9IHtcbiAgICAgIHRpdGxlOiBlZGl0aW5nVGl0bGUsXG4gICAgICBjb3Vyc2VHb2FscyxcbiAgICAgIGNvdXJzZU9iamVjdGl2ZXMsXG4gICAgICBjb3Vyc2VMaXN0XG4gICAgfTtcbiAgICBvblNhdmUoZGF0YSk7XG4gICAgb25DbG9zZSgpO1xuICB9O1xuXG4gIC8vIOWPkeW4g+ezu+WIl+ivvueoi1xuICBjb25zdCBoYW5kbGVQdWJsaXNoID0gKCkgPT4ge1xuICAgIC8vIFRPRE86IOWunueOsOWPkeW4g+mAu+i+kVxuICAgIGFsZXJ0KCflj5HluIPns7vliJfor77nqIvlip/og73lvoXlrp7njrAnKTtcbiAgfTtcblxuICAvLyDpgIDlh7rnvJbovpHmqKHlvI9cbiAgY29uc3QgaGFuZGxlRXhpdEVkaXQgPSAoKSA9PiB7XG4gICAgb25DbG9zZSgpO1xuICB9O1xuXG4gIC8vIOaYvuekuuiuvue9rumdouadv1xuICBjb25zdCBzaG93U2V0dGluZ3NQYW5lbCA9ICgpID0+IHtcbiAgICBzZXRSaWdodFBhbmVsVHlwZSgnc2V0dGluZ3MnKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUlkKG51bGwpO1xuICB9O1xuXG4gIC8vIOaYvuekuuivvueoi+e8lui+kemdouadv1xuICBjb25zdCBzaG93Q291cnNlUGFuZWwgPSAoY291cnNlSWQ6IG51bWJlcikgPT4ge1xuICAgIHNldFJpZ2h0UGFuZWxUeXBlKCdjb3Vyc2UnKTtcbiAgICBzZXRTZWxlY3RlZENvdXJzZUlkKGNvdXJzZUlkKTtcbiAgfTtcblxuICAvLyDojrflj5bpgInkuK3nmoTor77nqItcbiAgY29uc3QgZ2V0U2VsZWN0ZWRDb3Vyc2UgPSAoKSA9PiB7XG4gICAgcmV0dXJuIGNvdXJzZUxpc3QuZmluZChjb3Vyc2UgPT4gY291cnNlLmlkID09PSBzZWxlY3RlZENvdXJzZUlkKTtcbiAgfTtcblxuICBpZiAoIWlzVmlzaWJsZSkgcmV0dXJuIG51bGw7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LW1vZGFsLW92ZXJsYXlcIj5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbW9kYWxcIj5cbiAgICAgICAgey8qIOWktOmDqCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1oZWFkZXJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LXRpdGxlLXNlY3Rpb25cIj5cbiAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC10aXRsZVwiPuivvueoi+WIl+ihqDwvaDI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWFjdGlvbnNcIj5cbiAgICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICAgIG9uQ2xpY2s9e3Nob3dTZXR0aW5nc1BhbmVsfVxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YGNvdXJzZS1saXN0LXNldHRpbmdzLWJ0biAke3JpZ2h0UGFuZWxUeXBlID09PSAnc2V0dGluZ3MnID8gJ2FjdGl2ZScgOiAnJ31gfVxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPFNldHRpbmdzIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxuICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXthZGROZXdDb3Vyc2V9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWFkZC1idG5cIj5cbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8YnV0dG9uIG9uQ2xpY2s9e29uQ2xvc2V9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWNsb3NlLWJ0blwiPlxuICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy02IGgtNlwiIC8+XG4gICAgICAgICAgPC9idXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiDkuLvopoHlhoXlrrkgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtY29udGVudFwiPlxuICAgICAgICAgIHsvKiDlt6bkvqfor77nqIvliJfooaggKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1zaWRlYmFyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWl0ZW1zXCI+XG4gICAgICAgICAgICAgIHtsb2FkaW5nID8gKFxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtbG9hZGluZ1wiPlxuICAgICAgICAgICAgICAgICAgPHA+5Yqg6L295LitLi4uPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICApIDogY291cnNlTGlzdC5sZW5ndGggPT09IDAgPyAoXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eVwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS1pY29uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxCb29rT3BlbiBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgdGV4dC1ncmF5LTMwMFwiIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS10aXRsZVwiPuaaguaXoOivvuaXtjwvaDM+XG4gICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS1kZXNjcmlwdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICDngrnlh7vlj7PkuIrop5LnmoQgKyDmjInpkq7mt7vliqDnrKzkuIDkuKror77ml7ZcbiAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgb25DbGljaz17YWRkTmV3Q291cnNlfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1lbXB0eS1idG5cIlxuICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cbiAgICAgICAgICAgICAgICAgICAg5re75Yqg6K++5pe2XG4gICAgICAgICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICBjb3Vyc2VMaXN0Lm1hcCgoY291cnNlKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8ZGl2XG4gICAgICAgICAgICAgICAgICAgIGtleT17Y291cnNlLmlkfVxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bjb3Vyc2UtbGlzdC1pdGVtICR7c2VsZWN0ZWRDb3Vyc2VJZCA9PT0gY291cnNlLmlkID8gJ2FjdGl2ZScgOiAnJ31gfVxuICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzaG93Q291cnNlUGFuZWwoY291cnNlLmlkKX1cbiAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtaXRlbS10ZXh0XCI+e2NvdXJzZS50aXRsZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xuICAgICAgICAgICAgICAgICAgICAgICAgZS5zdG9wUHJvcGFnYXRpb24oKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGRlbGV0ZUNvdXJzZShjb3Vyc2UuaWQpO1xuICAgICAgICAgICAgICAgICAgICAgIH19XG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtaXRlbS1kZWxldGVcIlxuICAgICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgICAgPFggY2xhc3NOYW1lPVwidy0zIGgtM1wiIC8+XG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgKSlcbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIOWPs+S+p+e8lui+keWMuuWfnyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWVkaXQtYXJlYVwiPlxuICAgICAgICAgICAge3JpZ2h0UGFuZWxUeXBlID09PSAnbm9uZScgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWVtcHR5XCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS1pY29uXCI+XG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwidy0xNiBoLTE2IHRleHQtZ3JheS0zMDBcIiAvPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDxoMyBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1lbXB0eS10aXRsZVwiPuaXoOivvueoi+ivpuaDhTwvaDM+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZW1wdHktZGVzY3JpcHRpb25cIj5cbiAgICAgICAgICAgICAgICAgIOeCueWHu+W3puS+p+ivvueoi+aIluiuvue9ruaMiemSruafpeeci+ivpuaDhVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICApfVxuXG4gICAgICAgICAgICB7cmlnaHRQYW5lbFR5cGUgPT09ICdzZXR0aW5ncycgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIHsvKiDns7vliJfor77nqIvlsIHpnaIgKi99XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2Utc2VyaWVzLWNvdmVyXCI+XG4gICAgICAgICAgICAgICAgICB7c2VyaWVzQ292ZXJJbWFnZSA/IChcbiAgICAgICAgICAgICAgICAgICAgPGltZ1xuICAgICAgICAgICAgICAgICAgICAgIHNyYz17c2VyaWVzQ292ZXJJbWFnZX1cbiAgICAgICAgICAgICAgICAgICAgICBhbHQ9XCLns7vliJfor77nqIvlsIHpnaJcIlxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1zZXJpZXMtY292ZXItaW1hZ2VcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2Utc2VyaWVzLWNvdmVyLXBsYWNlaG9sZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4+57O75YiX6K++56iL5bCB6Z2iPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICB7Lyog57O75YiX6K6+572u6KGo5Y2VICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZm9ybVwiPlxuICAgICAgICAgICAgICAgICAgey8qIOezu+WIl+ivvueoi+agh+mimCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+57O75YiX6K++56iL5qCH6aKYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtlZGl0aW5nVGl0bGV9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBzZXRFZGl0aW5nVGl0bGUoZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeezu+WIl+ivvueoi+agh+mimFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+agh+etviAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+6K++56iL5qCH562+PC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPFNlbGVjdFxuICAgICAgICAgICAgICAgICAgICAgIG1vZGU9XCJtdWx0aXBsZVwiXG4gICAgICAgICAgICAgICAgICAgICAgc3R5bGU9e3sgd2lkdGg6ICcxMDAlJyB9fVxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36YCJ5oup6K++56iL5qCH562+XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17c2VsZWN0ZWRUYWdzfVxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXtzZXRTZWxlY3RlZFRhZ3N9XG4gICAgICAgICAgICAgICAgICAgICAgbG9hZGluZz17dGFnc0xvYWRpbmd9XG4gICAgICAgICAgICAgICAgICAgICAgb3B0aW9ucz17Y291cnNlVGFncy5tYXAodGFnID0+ICh7XG4gICAgICAgICAgICAgICAgICAgICAgICBsYWJlbDogKFxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBzdHlsZT17eyBjb2xvcjogdGFnLmNvbG9yIH19PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0YWcubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgKSxcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhbHVlOiB0YWcuaWRcbiAgICAgICAgICAgICAgICAgICAgICB9KSl9XG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+mhueebruaIkOWRmCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+6K++56iL6aG555uu5oiQ5ZGYPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgICAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtwcm9qZWN0TWVtYmVyc31cbiAgICAgICAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFByb2plY3RNZW1iZXJzKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1pbnB1dFwiXG4gICAgICAgICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLor7fovpPlhaXpobnnm67miJDlkZjvvIzlpoLvvJrlvKDogIHluIjjgIHmnY7liqnmlZnjgIHnjovlkIzlraZcIlxuICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKX1cblxuICAgICAgICAgICAge3JpZ2h0UGFuZWxUeXBlID09PSAnY291cnNlJyAmJiBnZXRTZWxlY3RlZENvdXJzZSgpICYmIChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICB7Lyog6K++56iL5bCB6Z2iICovfVxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLXNlcmllcy1jb3ZlclwiPlxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2Utc2VyaWVzLWNvdmVyLXBsYWNlaG9sZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxzcGFuPuivvueoi+Wwgemdojwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgey8qIOivvueoi+e8lui+keihqOWNlSAqL31cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWZvcm1cIj5cbiAgICAgICAgICAgICAgICAgIHsvKiDor77nqIvmoIfpopggKi99XG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWZpZWxkXCI+XG4gICAgICAgICAgICAgICAgICAgIDxsYWJlbCBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1sYWJlbFwiPuivvueoi+agh+mimDwvbGFiZWw+XG4gICAgICAgICAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICAgICAgICAgIHR5cGU9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17Z2V0U2VsZWN0ZWRDb3Vyc2UoKT8udGl0bGUgfHwgJyd9XG4gICAgICAgICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiB1cGRhdGVDb3Vyc2VUaXRsZShzZWxlY3RlZENvdXJzZUlkISwgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWlucHV0XCJcbiAgICAgICAgICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIuivt+i+k+WFpeivvueoi+agh+mimFwiXG4gICAgICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOivvueoi+aPj+i/sCAqL31cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgPGxhYmVsIGNsYXNzTmFtZT1cImNvdXJzZS1lZGl0LWxhYmVsXCI+6K++56iL5o+P6L+wPC9sYWJlbD5cbiAgICAgICAgICAgICAgICAgICAgPHRleHRhcmVhXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtdGV4dGFyZWFcIlxuICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPVwi6K+36L6T5YWl6K++56iL5o+P6L+wXCJcbiAgICAgICAgICAgICAgICAgICAgICByb3dzPXs0fVxuICAgICAgICAgICAgICAgICAgICAgIHZhbHVlPXtnZXRTZWxlY3RlZENvdXJzZSgpPy5kZXNjcmlwdGlvbiB8fCAnJ31cbiAgICAgICAgICAgICAgICAgICAgLz5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICB7Lyog6K++56iL54q25oCBICovfVxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtZWRpdC1maWVsZFwiPlxuICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7or77nqIvnirbmgIE8L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJjb3Vyc2Utc3RhdHVzLWxhYmVsXCI+XG4gICAgICAgICAgICAgICAgICAgICAge2dldFNlbGVjdGVkQ291cnNlKCk/LnN0YXR1c0xhYmVsfVxuICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgey8qIOinhumikeS/oeaBryAqL31cbiAgICAgICAgICAgICAgICAgIHtnZXRTZWxlY3RlZENvdXJzZSgpPy5oYXNWaWRlbyA9PT0gMSAmJiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtZmllbGRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8bGFiZWwgY2xhc3NOYW1lPVwiY291cnNlLWVkaXQtbGFiZWxcIj7op4bpopHml7bplb88L2xhYmVsPlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImNvdXJzZS12aWRlby1kdXJhdGlvblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAge2dldFNlbGVjdGVkQ291cnNlKCk/LnZpZGVvRHVyYXRpb25MYWJlbH1cbiAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7Lyog5bqV6YOo5oyJ6ZKuICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWZvb3RlclwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtZm9vdGVyLWxlZnRcIj5cbiAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlUHVibGlzaH0gY2xhc3NOYW1lPVwiY291cnNlLWxpc3QtYnRuIGNvdXJzZS1saXN0LWJ0bi1wdWJsaXNoXCI+XG4gICAgICAgICAgICAgIOWPkeW4g+ezu+WIl+ivvueoi1xuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1mb290ZXItcmlnaHRcIj5cbiAgICAgICAgICAgIDxidXR0b24gb25DbGljaz17aGFuZGxlRXhpdEVkaXR9IGNsYXNzTmFtZT1cImNvdXJzZS1saXN0LWJ0biBjb3Vyc2UtbGlzdC1idG4tZXhpdFwiPlxuICAgICAgICAgICAgICDpgIDlh7rnvJbovpHmqKHlvI9cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICAgPGJ1dHRvbiBvbkNsaWNrPXtoYW5kbGVTYXZlfSBjbGFzc05hbWU9XCJjb3Vyc2UtbGlzdC1idG4gY291cnNlLWxpc3QtYnRuLXNhdmVcIj5cbiAgICAgICAgICAgICAg5L+d5a2YXG4gICAgICAgICAgICA8L2J1dHRvbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENvdXJzZUxpc3RFZGl0TW9kYWw7XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlgiLCJTZXR0aW5ncyIsIlBsdXMiLCJCb29rT3BlbiIsIkZpbGVUZXh0IiwiY291cnNlTWFuYWdlbWVudEFwaSIsImNvdXJzZUFwaSIsIlNlbGVjdCIsImZldGNoQ291cnNlTGlzdCIsInNlcmllc0lkIiwiZ2V0U2VyaWVzQ291cnNlcyIsImZldGNoQ291cnNlVGFncyIsImdldENvdXJzZVRhZ3MiLCJwYWdlIiwicGFnZVNpemUiLCJzdGF0dXMiLCJDb3Vyc2VMaXN0RWRpdE1vZGFsIiwiaXNWaXNpYmxlIiwib25DbG9zZSIsIm9uU2F2ZSIsInNlcmllc1RpdGxlIiwic2VyaWVzQ292ZXJJbWFnZSIsImdldFNlbGVjdGVkQ291cnNlIiwiY291cnNlTGlzdCIsInNldENvdXJzZUxpc3QiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInJpZ2h0UGFuZWxUeXBlIiwic2V0UmlnaHRQYW5lbFR5cGUiLCJzZWxlY3RlZENvdXJzZUlkIiwic2V0U2VsZWN0ZWRDb3Vyc2VJZCIsImVkaXRpbmdUaXRsZSIsInNldEVkaXRpbmdUaXRsZSIsImNvdXJzZUdvYWxzIiwic2V0Q291cnNlR29hbHMiLCJjb3Vyc2VPYmplY3RpdmVzIiwic2V0Q291cnNlT2JqZWN0aXZlcyIsInByb2plY3RNZW1iZXJzIiwic2V0UHJvamVjdE1lbWJlcnMiLCJjb3Vyc2VUYWdzIiwic2V0Q291cnNlVGFncyIsInNlbGVjdGVkVGFncyIsInNldFNlbGVjdGVkVGFncyIsInRhZ3NMb2FkaW5nIiwic2V0VGFnc0xvYWRpbmciLCJ0b2tlbiIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJjb25zb2xlIiwibG9nIiwiZXJyb3IiLCJsb2FkQ291cnNlTGlzdCIsImxvYWRDb3Vyc2VUYWdzIiwicmVzcG9uc2UiLCJjb2RlIiwiZGF0YSIsImNvdXJzZXMiLCJsaXN0IiwibWVzc2FnZSIsImFkZE5ld0NvdXJzZSIsIm5ld0NvdXJzZSIsImlkIiwiRGF0ZSIsIm5vdyIsInRpdGxlIiwibGVuZ3RoIiwiZGVzY3JpcHRpb24iLCJjb3ZlckltYWdlIiwib3JkZXJJbmRleCIsInN0YXR1c0xhYmVsIiwiaGFzVmlkZW8iLCJoYXNEb2N1bWVudCIsImhhc0F1ZGlvIiwidmlkZW9EdXJhdGlvbiIsInZpZGVvRHVyYXRpb25MYWJlbCIsInZpZGVvTmFtZSIsImZpcnN0VGVhY2hpbmdUaXRsZSIsInJlc291cmNlc0NvdW50IiwiY3JlYXRlZEF0IiwidG9JU09TdHJpbmciLCJ1cGRhdGVkQXQiLCJzaG93Q291cnNlUGFuZWwiLCJkZWxldGVDb3Vyc2UiLCJmaWx0ZXIiLCJjb3Vyc2UiLCJ1cGRhdGVDb3Vyc2VUaXRsZSIsIm5ld1RpdGxlIiwibWFwIiwiaGFuZGxlU2F2ZSIsImhhbmRsZVB1Ymxpc2giLCJhbGVydCIsImhhbmRsZUV4aXRFZGl0Iiwic2hvd1NldHRpbmdzUGFuZWwiLCJjb3Vyc2VJZCIsImZpbmQiLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwIiwiaDMiLCJzcGFuIiwiZSIsInN0b3BQcm9wYWdhdGlvbiIsImltZyIsInNyYyIsImFsdCIsImxhYmVsIiwiaW5wdXQiLCJ0eXBlIiwidmFsdWUiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwibW9kZSIsInN0eWxlIiwid2lkdGgiLCJvcHRpb25zIiwidGFnIiwiY29sb3IiLCJuYW1lIiwidGV4dGFyZWEiLCJyb3dzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api/course.ts":
/*!***************************!*\
  !*** ./lib/api/course.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   courseApi: function() { return /* binding */ courseApi; }\n/* harmony export */ });\n/* harmony import */ var _request__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../request */ \"(app-pages-browser)/./lib/request.ts\");\n// import { request } from './common';\n\n// 课程API\nconst courseApi = {\n    baseUrl: \"/api/v1/course-management\",\n    getMyCourseSeriesList: (params)=>{\n        // 设置默认参数\n        const requestParams = {\n            page: 1,\n            pageSize: 10,\n            ...params\n        };\n        console.log(\"\\uD83D\\uDCE4 获取我的课程系列列表:\", requestParams);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/my-series\"), {\n            params: requestParams\n        });\n    },\n    // 获取系列下的课程列表 - 使用课程市场API\n    getSeriesCourseList: (seriesId, params)=>{\n        console.log(\"\\uD83C\\uDF10 courseApi.getSeriesCourseList 调用（课程市场API）\");\n        console.log(\"\\uD83D\\uDCE4 系列ID:\", seriesId, \"参数:\", params);\n        console.log(\"\\uD83D\\uDD17 接口地址: GET /api/v1/course-marketplace/series/{seriesId}/courses\");\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/series/\".concat(seriesId, \"/courses\"), {\n            params\n        });\n    },\n    // 获取课程列表 - 暂时不需要，已删除\n    // getCourseList: (params?: {\n    //   page?: number;\n    //   pageSize?: number;\n    //   keyword?: string;\n    //   category?: string;\n    //   status?: string;\n    // }) => {\n    //   return request.get('/api/course/list', {\n    //     params: {\n    //       page: 1,\n    //       pageSize: 10,\n    //       ...params\n    //     }\n    //   });\n    // },\n    // 获取单个课程详情 - 暂时不需要，已删除\n    // getCourseById: (id: number) => {\n    //   return request.get(`/api/course/${id}`);\n    // },\n    // 获取系列下的课程列表\n    getSeriesCourses: (seriesId, params)=>{\n        console.log(\"\\uD83C\\uDF10 courseApi.getSeriesCourses 调用（课程市场API）\");\n        console.log(\"\\uD83D\\uDCE4 系列ID:\", seriesId, \"参数:\", params);\n        console.log(\"\\uD83D\\uDD17 接口地址: GET /api/v1/course-marketplace/series/{seriesId}/courses\");\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/series/\".concat(seriesId, \"/courses\"), {\n            params\n        });\n    },\n    // 创建课程\n    createCourse: (data)=>{\n        console.log(\"\\uD83D\\uDCE4 发送课程创建请求到:\", \"\".concat(courseApi.baseUrl, \"/courses\"));\n        console.log(\"\\uD83D\\uDCE4 请求数据:\", data);\n        // 为课程创建请求设置更长的超时时间，因为可能包含大文件\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(courseApi.baseUrl, \"/courses\"), data, {\n            timeout: 60000,\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n    },\n    // 获取课程详情\n    getCourseDetail: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/courses/\").concat(id));\n    },\n    // 获取我的系列课程列表\n    // 接口地址: GET /api/v1/course-management/my-series\n    // 接口描述: 获取当前用户创建的系列课程列表，支持分页和筛选\n    getMySeries: (params)=>{\n        console.log(\"\\uD83C\\uDF10 courseApi.getMySeries 调用\");\n        console.log(\"\\uD83D\\uDCE4 请求参数:\", params);\n        console.log(\"\\uD83D\\uDD17 接口地址: GET /api/v1/course-management/my-series\");\n        console.log(\"\\uD83D\\uDCCB 支持参数: page, pageSize, status(0=草稿,1=已发布,2=已归档), keyword\");\n        // 设置默认参数\n        const requestParams = {\n            page: 1,\n            pageSize: 10,\n            ...params\n        };\n        console.log(\"\\uD83D\\uDCE4 最终请求参数:\", requestParams);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/my-series\"), {\n            params: requestParams\n        });\n    },\n    // 获取系列课程详情\n    getSeriesDetail: (id)=>{\n        console.log(\"\\uD83D\\uDCE4 发送系列详情请求到:\", \"\".concat(courseApi.baseUrl, \"/series/\").concat(id));\n        console.log(\"\\uD83D\\uDCE4 系列ID:\", id);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/series/\").concat(id));\n    },\n    // 获取课程市场的系列详情\n    getMarketplaceSeriesDetail: (seriesId)=>{\n        console.log(\"\\uD83D\\uDCE4 发送课程市场系列详情请求到:\", \"/api/v1/course-marketplace/series/\".concat(seriesId));\n        console.log(\"\\uD83D\\uDCE4 系列ID:\", seriesId);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/series/\".concat(seriesId));\n    },\n    // 获取课程市场的课程详情\n    getCourseMarketplaceDetail: (seriesId, courseId)=>{\n        console.log(\"\\uD83D\\uDCE4 发送课程市场详情请求到:\", \"/api/v1/course-marketplace/series/\".concat(seriesId, \"/courses/\").concat(courseId));\n        console.log(\"\\uD83D\\uDCE4 系列ID:\", seriesId, \"课程ID:\", courseId);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/series/\".concat(seriesId, \"/courses/\").concat(courseId));\n    },\n    // 创建系列课程\n    createCourseSeries: (data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(courseApi.baseUrl, \"/series\"), data);\n    },\n    // 更新系列课程\n    updateCourseSeries: (id, data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"\".concat(courseApi.baseUrl, \"/series/\").concat(id), data);\n    },\n    // 删除系列课程\n    deleteCourseSeries: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"\".concat(courseApi.baseUrl, \"/series/\").concat(id));\n    },\n    // 发布系列课程\n    publishCourseSeries: (seriesId)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(courseApi.baseUrl, \"/series/\").concat(seriesId, \"/publish\"));\n    },\n    // 发布课程\n    publishCourse: (courseId)=>{\n        console.log(\"\\uD83D\\uDCE4 发送发布课程请求到:\", \"\".concat(courseApi.baseUrl, \"/courses/\").concat(courseId, \"/publish\"));\n        console.log(\"\\uD83D\\uDCE4 课程ID:\", courseId);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"\".concat(courseApi.baseUrl, \"/courses/\").concat(courseId, \"/publish\"));\n    },\n    // 更新课程\n    updateCourse: (id, data)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"\".concat(courseApi.baseUrl, \"/courses/\").concat(id), data);\n    },\n    // 删除课程\n    deleteCourse: (id)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"\".concat(courseApi.baseUrl, \"/courses/\").concat(id));\n    },\n    // 调整课程排序\n    updateCourseOrders: (seriesId, courseOrders)=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"\".concat(courseApi.baseUrl, \"/series/\").concat(seriesId, \"/course-orders\"), {\n            courseOrders\n        });\n    },\n    // 批量删除课程 - 暂时不需要，已删除\n    // batchDeleteCourses: (ids: number[]) => {\n    //   return request.post('/api/course/batch-delete', { ids });\n    // },\n    // 更新课程状态 - 暂时不需要，已删除\n    // updateCourseStatus: (id: number, status: 'active' | 'inactive') => {\n    //   return request.patch(`/api/course/${id}/status`, { status });\n    // },\n    // 获取课程分类列表 - 暂时不需要，已删除\n    // getCourseCategories: () => {\n    //   return request.get('/api/course/categories');\n    // },\n    // 搜索课程 - 暂时不需要，已删除\n    // searchCourses: (keyword: string, params?: any) => {\n    //   return request.get('/api/course/search', {\n    //     params: {\n    //       keyword,\n    //       page: 1,\n    //       pageSize: 10,\n    //       ...params\n    //     }\n    //   });\n    // },\n    // 获取教师列表\n    getTeachers: ()=>{\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/teachers\"));\n    },\n    // 获取课程标签列表 - 使用课程市场API\n    getCourseTags: (params)=>{\n        console.log(\"\\uD83C\\uDFF7️ 获取课程标签列表，参数:\", params);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/tags\", {\n            params: {\n                page: 1,\n                pageSize: 100,\n                status: 1,\n                ...params\n            }\n        });\n    },\n    // 创建课程标签\n    createCourseTag: (data)=>{\n        console.log(\"\\uD83C\\uDFF7️ 创建课程标签，数据:\", data);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/api/v1/course-marketplace/tags\", data);\n    },\n    // 更新课程标签\n    updateCourseTag: (id, data)=>{\n        console.log(\"\\uD83C\\uDFF7️ 更新课程标签，ID:\", id, \"数据:\", data);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/api/v1/course-marketplace/tags/\".concat(id), data);\n    },\n    // 删除课程标签\n    deleteCourseTag: (id)=>{\n        console.log(\"\\uD83C\\uDFF7️ 删除课程标签，ID:\", id);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/api/v1/course-marketplace/tags/\".concat(id));\n    },\n    // 获取单个标签详情\n    getCourseTagById: (id)=>{\n        console.log(\"\\uD83C\\uDFF7️ 获取标签详情，ID:\", id);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/tags/\".concat(id));\n    },\n    // 获取课程市场系列课程列表\n    getMarketplaceSeries: (params)=>{\n        console.log(\"\\uD83C\\uDF10 courseApi.getMarketplaceSeries 调用\");\n        console.log(\"\\uD83D\\uDCE4 请求参数:\", params);\n        console.log(\"\\uD83D\\uDD17 接口地址: GET /api/v1/course-marketplace/series\");\n        console.log('\\uD83D\\uDCCB 注意：前端使用categoryLabel字段(\"官方\"/\"社区\")进行筛选');\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/api/v1/course-marketplace/series\", {\n            params: {\n                page: 1,\n                pageSize: 50,\n                ...params\n            }\n        });\n    },\n    // 获取课程系列列表\n    getCourseSeries: (params)=>{\n        console.log(\"\\uD83D\\uDD04 开始获取系列课程列表，参数:\", params);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/series\"), {\n            params: {\n                page: 1,\n                pageSize: 10,\n                ...params\n            }\n        });\n    },\n    // 根据手机号查询教师\n    searchTeacherByPhone: (phone)=>{\n        console.log(\"发起手机号查询请求:\", phone);\n        return _request__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"\".concat(courseApi.baseUrl, \"/teachers/search-by-phone\"), {\n            params: {\n                phone\n            }\n        });\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api/course.ts\n"));

/***/ })

});