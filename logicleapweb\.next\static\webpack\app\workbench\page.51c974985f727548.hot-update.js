"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CourseManagement.tsx":
/*!*******************************************************!*\
  !*** ./app/workbench/components/CourseManagement.tsx ***!
  \*******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Image,Plus,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _CreateSeriesCourseModal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CreateSeriesCourseModal */ \"(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.tsx\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _CourseManagement_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CourseManagement.css */ \"(app-pages-browser)/./app/workbench/components/CourseManagement.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst CourseManagement = ()=>{\n    _s();\n    const [isCreateModalVisible, setIsCreateModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSeriesModalVisible, setIsSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\",\n        category: \"\",\n        tags: \"\"\n    });\n    // 课程数据状态\n    const [courses, setCourses] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 课程列表编辑弹窗状态\n    const [isCourseListModalVisible, setIsCourseListModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedCourse, setSelectedCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // 获取课程列表\n    const fetchCourses = async ()=>{\n        try {\n            setLoading(true);\n            setError(null);\n            const response = await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_3__.courseManagementApi.getMySeries({\n                page: 1,\n                pageSize: 20\n            });\n            if (response.code === 200) {\n                // 转换API数据格式为本地Course格式\n                const coursesData = response.data.list.map((item)=>({\n                        id: item.id,\n                        title: item.title,\n                        description: item.description,\n                        coverImage: item.coverImage,\n                        status: item.status === 0 ? \"draft\" : item.status === 1 ? \"published\" : \"offline\",\n                        statusLabel: item.statusLabel,\n                        totalCourses: item.totalCourses,\n                        totalStudents: item.totalStudents,\n                        createTime: new Date(item.createdAt).toLocaleDateString(\"zh-CN\", {\n                            year: \"numeric\",\n                            month: \"2-digit\",\n                            day: \"2-digit\"\n                        }).replace(/\\//g, \".\")\n                    }));\n                setCourses(coursesData);\n                console.log(\"课程列表加载成功:\", coursesData);\n            } else {\n                throw new Error(response.message || \"获取课程列表失败\");\n            }\n        } catch (error) {\n            console.error(\"获取课程列表失败:\", error);\n            setError(error instanceof Error ? error.message : \"获取课程列表失败\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // 组件挂载时获取课程列表\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourses();\n    }, []);\n    // 处理创建课程\n    const handleCreateCourse = async (e)=>{\n        if (e) e.preventDefault();\n        // 检查标题是否为空\n        if (!formData.title.trim()) {\n            alert(\"请输入系列课程标题\");\n            return;\n        }\n        try {\n            console.log(\"创建系列课程数据:\", formData);\n            // 添加新课程到列表\n            const newCourse = {\n                id: courses.length + 1,\n                title: formData.title,\n                description: \"创建于\".concat(new Date().getFullYear(), \".\").concat(String(new Date().getMonth() + 1).padStart(2, \"0\"), \".\").concat(String(new Date().getDate()).padStart(2, \"0\")),\n                coverImage: \"/api/placeholder/280/160\",\n                status: \"draft\",\n                createTime: \"\".concat(new Date().getFullYear(), \".\").concat(String(new Date().getMonth() + 1).padStart(2, \"0\"), \".\").concat(String(new Date().getDate()).padStart(2, \"0\"))\n            };\n            setCourses((prev)=>[\n                    newCourse,\n                    ...prev\n                ]);\n            alert(\"系列课程创建成功！\");\n            setIsCreateModalVisible(false);\n            setFormData({\n                title: \"\",\n                description: \"\",\n                category: \"\",\n                tags: \"\"\n            });\n        } catch (error) {\n            console.error(\"创建系列课程失败:\", error);\n            alert(\"创建系列课程失败，请重试\");\n        }\n    };\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // 处理系列课程创建\n    const handleCreateSeriesCourse = async (data)=>{\n        try {\n            console.log(\"创建系列课程成功:\", data);\n            // 刷新课程列表以获取最新数据\n            await fetchCourses();\n            console.log(\"课程列表已刷新\");\n        } catch (error) {\n            console.error(\"刷新课程列表失败:\", error);\n        }\n    };\n    // 获取状态按钮样式和文本\n    const getStatusButton = (status)=>{\n        switch(status){\n            case \"draft\":\n                return {\n                    text: \"草稿\",\n                    className: \"px-4 py-1 bg-gray-100 text-gray-600 rounded-full text-sm border border-gray-300\"\n                };\n            case \"published\":\n                return {\n                    text: \"发布\",\n                    className: \"px-4 py-1 bg-blue-100 text-blue-600 rounded-full text-sm border border-blue-300\"\n                };\n            case \"offline\":\n                return {\n                    text: \"下架\",\n                    className: \"px-4 py-1 bg-orange-100 text-orange-600 rounded-full text-sm border border-orange-300\"\n                };\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"h-full bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-white border-b border-gray-200 px-6 py-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"课程管理\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsSeriesModalVisible(true),\n                            className: \"create-course-btn\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"create-course-btn-icon\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-3 h-3\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 13\n                                }, undefined),\n                                \"创建课程\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-6\",\n                children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"加载中...\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"正在获取课程列表\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 190,\n                    columnNumber: 11\n                }, undefined) : error ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-8 h-8 text-red-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"加载失败\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 205,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-6\",\n                                children: error\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: fetchCourses,\n                                className: \"flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200 font-medium mx-auto\",\n                                children: \"重新加载\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                        lineNumber: 201,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, undefined) : courses.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-sm border border-gray-200 p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-8 h-8 text-gray-400\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                children: \"暂无课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 mb-6\",\n                                children: \"您还没有创建任何课程，点击右上角按钮开始创建您的第一个课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 15\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setIsSeriesModalVisible(true),\n                                className: \"flex items-center gap-2 px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors duration-200 font-medium mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    \"创建第一个课程\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 11\n                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                    children: courses.map((course)=>{\n                        const statusButton = getStatusButton(course.status);\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"course-card\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-cover\",\n                                    children: course.coverImage ? // 有封面图片时显示背景图（包括占位符）\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full\",\n                                        style: {\n                                            backgroundImage: \"url(\".concat(course.coverImage, \")\"),\n                                            backgroundSize: \"cover\",\n                                            backgroundPosition: \"center\",\n                                            backgroundRepeat: \"no-repeat\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 23\n                                    }, undefined) : // 没有封面时显示灰色背景和图标\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-full h-full bg-gray-100 flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"w-12 h-12 text-gray-400\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 25\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 23\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 239,\n                                    columnNumber: 19\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"course-info\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-start justify-between mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"course-title\",\n                                                        children: course.title\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: statusButton.className,\n                                                    children: statusButton.text\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 261,\n                                            columnNumber: 21\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-gray-500 pt-2 border-t border-gray-100\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"课程 \",\n                                                            course.totalCourses || 0\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 23\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: course.createTime\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 23\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 271,\n                                            columnNumber: 21\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 19\n                                }, undefined)\n                            ]\n                        }, course.id, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 237,\n                            columnNumber: 17\n                        }, undefined);\n                    })\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 188,\n                columnNumber: 7\n            }, undefined),\n            isCreateModalVisible && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white rounded-lg shadow-xl w-full max-w-md mx-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between p-6 border-b border-gray-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-xl font-semibold text-gray-900\",\n                                    children: \"创建系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 291,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>{\n                                        setIsCreateModalVisible(false);\n                                        setFormData({\n                                            title: \"\",\n                                            description: \"\",\n                                            category: \"\",\n                                            tags: \"\"\n                                        });\n                                    },\n                                    className: \"text-gray-400 hover:text-gray-600 transition-colors\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Image_Plus_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-6 h-6\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 290,\n                            columnNumber: 13\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6 space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-48 h-32 border-2 border-gray-300 rounded-lg flex items-center justify-center bg-gray-50\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-gray-500 text-sm\",\n                                            children: \"系列课程封面\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                            lineNumber: 308,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 306,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        name: \"title\",\n                                        value: formData.title,\n                                        onChange: handleInputChange,\n                                        placeholder: \"系列课程标题\",\n                                        className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 outline-none transition-colors text-center\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleCreateCourse,\n                                        className: \"px-8 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors duration-200\",\n                                        children: \"创建系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                    lineNumber: 288,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 287,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateSeriesCourseModal__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                isVisible: isSeriesModalVisible,\n                onClose: ()=>setIsSeriesModalVisible(false),\n                onSubmit: handleCreateSeriesCourse\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n                lineNumber: 339,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CourseManagement.tsx\",\n        lineNumber: 162,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CourseManagement, \"OAh8Sxvi/BHunp9NjEQlOqcwLhQ=\");\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CourseManagement.tsx\n"));

/***/ })

});