"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/workbench/page",{

/***/ "(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.tsx":
/*!**************************************************************!*\
  !*** ./app/workbench/components/CreateSeriesCourseModal.tsx ***!
  \**************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api/course-management */ \"(app-pages-browser)/./lib/api/course-management.ts\");\n/* harmony import */ var _CreateSeriesCourseModal_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./CreateSeriesCourseModal.css */ \"(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.css\");\n/* harmony import */ var _CourseListEditModal__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./CourseListEditModal */ \"(app-pages-browser)/./app/workbench/components/CourseListEditModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// 图片压缩函数\nconst compressImage = function(file) {\n    let quality = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0.7, maxWidth = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 800, maxHeight = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 600;\n    return new Promise((resolve)=>{\n        const canvas = document.createElement(\"canvas\");\n        const ctx = canvas.getContext(\"2d\");\n        const img = new Image();\n        img.onload = ()=>{\n            // 计算压缩后的尺寸\n            let { width, height } = img;\n            if (width > maxWidth || height > maxHeight) {\n                const ratio = Math.min(maxWidth / width, maxHeight / height);\n                width *= ratio;\n                height *= ratio;\n            }\n            canvas.width = width;\n            canvas.height = height;\n            // 绘制压缩后的图片\n            ctx.drawImage(img, 0, 0, width, height);\n            // 转换为Blob\n            canvas.toBlob((blob)=>{\n                if (blob) {\n                    const compressedFile = new File([\n                        blob\n                    ], file.name, {\n                        type: file.type,\n                        lastModified: Date.now()\n                    });\n                    resolve(compressedFile);\n                } else {\n                    resolve(file); // 如果压缩失败，返回原文件\n                }\n            }, file.type, quality);\n        };\n        img.src = URL.createObjectURL(file);\n    });\n};\n// 上传图片到服务器获取真实URL\nconst uploadImageToServer = async (file)=>{\n    console.log(\"开始上传图片到服务器:\", file.name);\n    // 先压缩图片\n    const compressedFile = await compressImage(file, 0.8, 1200, 800);\n    console.log(\"图片压缩完成，原大小:\", file.size, \"压缩后:\", compressedFile.size);\n    // 创建FormData\n    const formData = new FormData();\n    formData.append(\"file\", compressedFile);\n    formData.append(\"type\", \"course-cover\"); // 标识这是课程封面图片\n    try {\n        var _result_data;\n        // 发送到图片上传API\n        const response = await fetch(\"/api/v1/upload/image\", {\n            method: \"POST\",\n            body: formData\n        });\n        if (!response.ok) {\n            throw new Error(\"上传失败: \".concat(response.status, \" \").concat(response.statusText));\n        }\n        const result = await response.json();\n        console.log(\"图片上传响应:\", result);\n        if (result.code === 200 && ((_result_data = result.data) === null || _result_data === void 0 ? void 0 : _result_data.url)) {\n            console.log(\"图片上传成功，URL:\", result.data.url);\n            return result.data.url; // 返回图片URL\n        } else {\n            throw new Error(result.message || \"上传失败\");\n        }\n    } catch (error) {\n        console.error(\"图片上传错误:\", error);\n        throw error;\n    }\n};\nconst CreateSeriesCourseModal = (param)=>{\n    let { isVisible, onClose, onSubmit } = param;\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: \"\",\n        description: \"\"\n    });\n    const [coverImagePreview, setCoverImagePreview] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseListModalVisible, setIsCourseListModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createdSeriesData, setCreatedSeriesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    // 处理文件上传\n    const handleFileUpload = (e)=>{\n        var _e_target_files;\n        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n        if (file) {\n            // 检查文件类型\n            const allowedTypes = [\n                \"image/jpeg\",\n                \"image/jpg\",\n                \"image/png\",\n                \"image/gif\"\n            ];\n            if (!allowedTypes.includes(file.type)) {\n                alert(\"请选择 JPG、PNG 或 GIF 格式的图片\");\n                return;\n            }\n            // 检查文件大小 (5MB，更严格的限制)\n            const maxSize = 5 * 1024 * 1024;\n            if (file.size > maxSize) {\n                alert(\"图片大小不能超过 5MB，请选择更小的图片或使用图片压缩工具\");\n                return;\n            }\n            // 更新表单数据\n            setFormData((prev)=>({\n                    ...prev,\n                    coverImage: file\n                }));\n            // 创建预览URL\n            const reader = new FileReader();\n            reader.onload = (e)=>{\n                var _e_target;\n                setCoverImagePreview((_e_target = e.target) === null || _e_target === void 0 ? void 0 : _e_target.result);\n            };\n            reader.readAsDataURL(file);\n        }\n    };\n    // 移除封面图片\n    const removeCoverImage = ()=>{\n        setFormData((prev)=>({\n                ...prev,\n                coverImage: undefined\n            }));\n        setCoverImagePreview(null);\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!formData.title.trim()) {\n            alert(\"请输入系列课程标题\");\n            return;\n        }\n        // 检查是否上传了封面图片\n        if (!formData.coverImage) {\n            alert(\"请上传系列课程封面图片\");\n            return;\n        }\n        setIsSubmitting(true);\n        try {\n            console.log(\"开始构建请求数据...\");\n            // 根据后端错误信息，后端期望纯JSON格式\n            // coverImage必须是字符串，不能是文件对象\n            let coverImageUrl = \"\";\n            // 上传图片到服务器获取真实URL（必需）\n            console.log(\"开始上传图片文件:\", formData.coverImage.name, formData.coverImage.size);\n            try {\n                setIsUploading(true);\n                // 上传图片到服务器\n                coverImageUrl = await uploadImageToServer(formData.coverImage);\n                console.log(\"图片上传成功，获得URL:\", coverImageUrl);\n                // 验证返回的URL是否有效\n                if (!coverImageUrl || !coverImageUrl.startsWith(\"http\")) {\n                    throw new Error(\"图片上传返回的URL无效\");\n                }\n            } catch (error) {\n                console.error(\"图片上传失败:\", error);\n                alert(\"图片上传失败，请重试: \" + (error instanceof Error ? error.message : \"未知错误\"));\n                return; // 上传失败时直接返回，不创建课程\n            } finally{\n                setIsUploading(false);\n            }\n            // 构建JSON数据，包含必需字段\n            const requestData = {\n                title: formData.title,\n                category: 0,\n                coverImage: coverImageUrl // 必需字段：真实图片URL\n            };\n            // 可选字段：只有用户输入了description才添加\n            if (formData.description && formData.description.trim()) {\n                requestData.description = formData.description.trim();\n            }\n            console.log(\"添加真实图片URL到请求:\", coverImageUrl);\n            // 注意：不发送 projectMembers 和 tagIds 字段\n            // 这些字段可以在后续的编辑功能中添加\n            console.log(\"发送的数据:\", requestData);\n            console.log(\"构建的JSON数据:\", requestData);\n            // 发送JSON请求\n            const response = await _lib_api_course_management__WEBPACK_IMPORTED_MODULE_2__.courseManagementApi.createSeriesCourse(requestData);\n            if (response.code === 200) {\n                console.log(\"系列课程创建成功:\", response.data);\n                // 保存创建的系列课程数据\n                setCreatedSeriesData({\n                    ...response.data,\n                    coverImage: coverImageUrl,\n                    title: formData.title\n                });\n                // 显示课程列表编辑弹窗\n                setIsCourseListModalVisible(true);\n                // 通知父组件\n                onSubmit(response.data);\n            } else {\n                throw new Error(response.message || \"创建失败\");\n            }\n        } catch (error) {\n            console.error(\"创建系列课程失败:\", error);\n            // 显示更详细的错误信息\n            let errorMessage = \"创建系列课程失败，请重试\";\n            if (error.response) {\n                var _error_response_data;\n                console.error(\"错误响应:\", error.response.data);\n                console.error(\"错误状态:\", error.response.status);\n                errorMessage = \"请求失败 (\".concat(error.response.status, \"): \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.response.statusText);\n            } else if (error.request) {\n                console.error(\"网络错误:\", error.request);\n                errorMessage = \"网络连接失败，请检查网络连接\";\n            } else {\n                console.error(\"请求配置错误:\", error.message);\n                errorMessage = error.message;\n            }\n            alert(errorMessage);\n        } finally{\n            setIsSubmitting(false);\n        }\n    };\n    const handleClose = ()=>{\n        setFormData({\n            title: \"\",\n            description: \"\"\n        });\n        setCoverImagePreview(null);\n        setIsSubmitting(false);\n        setIsUploading(false);\n        setIsCourseListModalVisible(false);\n        setCreatedSeriesData(null);\n        onClose();\n    };\n    // 处理课程列表编辑弹窗关闭\n    const handleCourseListModalClose = ()=>{\n        setIsCourseListModalVisible(false);\n        // 关闭课程列表编辑弹窗时，也关闭创建系列课程弹窗\n        handleClose();\n    };\n    // 处理课程列表保存\n    const handleCourseListSave = (data)=>{\n        console.log(\"保存课程列表数据:\", data);\n        // TODO: 调用API保存课程列表数据\n        alert(\"课程列表保存成功！\");\n    };\n    if (!isVisible) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"series-modal-overlay\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"series-modal\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"series-modal-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"series-modal-title\",\n                                children: \"创建系列课程\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleClose,\n                                className: \"series-modal-close\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"w-6 h-6\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"series-modal-content\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"series-cover-section\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"series-cover-container\",\n                                    children: coverImagePreview ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"series-cover-preview\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: coverImagePreview,\n                                                alt: \"课程封面预览\",\n                                                className: \"series-cover-image\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: removeCoverImage,\n                                                className: \"series-cover-remove\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        className: \"series-cover-placeholder\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                type: \"file\",\n                                                accept: \"image/jpeg,image/jpg,image/png,image/gif\",\n                                                onChange: handleFileUpload,\n                                                className: \"series-cover-input\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                className: \"w-6 h-6 text-gray-400 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"series-cover-text\",\n                                                children: \"系列课程封面 *\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"series-cover-hint\",\n                                                children: \"点击上传图片（必需）\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 310,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"series-form-group\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    name: \"title\",\n                                    value: formData.title,\n                                    onChange: handleInputChange,\n                                    placeholder: \"系列课程标题\",\n                                    className: \"series-title-input\",\n                                    required: true\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"series-form-actions\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"submit\",\n                                    disabled: isSubmitting,\n                                    className: \"series-create-btn \".concat(isSubmitting ? \"series-create-btn-loading\" : \"\"),\n                                    children: isSubmitting ? \"创建中...\" : \"创建系列课程\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                        lineNumber: 307,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, undefined),\n            createdSeriesData && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CourseListEditModal__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                isVisible: isCourseListModalVisible,\n                onClose: handleCourseListModalClose,\n                onSave: handleCourseListSave,\n                seriesTitle: createdSeriesData.title,\n                seriesCoverImage: createdSeriesData.coverImage,\n                seriesId: createdSeriesData.id\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n                lineNumber: 370,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\workbench\\\\components\\\\CreateSeriesCourseModal.tsx\",\n        lineNumber: 293,\n        columnNumber: 5\n    }, undefined);\n};\n_s(CreateSeriesCourseModal, \"lz6s6B5KdBPGF8nsXze0vZOkNKI=\");\n_c = CreateSeriesCourseModal;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CreateSeriesCourseModal);\nvar _c;\n$RefreshReg$(_c, \"CreateSeriesCourseModal\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/workbench/components/CreateSeriesCourseModal.tsx\n"));

/***/ })

});