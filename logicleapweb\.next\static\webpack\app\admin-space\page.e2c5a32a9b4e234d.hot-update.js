"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-space/page",{

/***/ "(app-pages-browser)/./app/admin-space/components/course-management.tsx":
/*!**********************************************************!*\
  !*** ./app/admin-space/components/course-management.tsx ***!
  \**********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/input/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/select/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/form/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/message/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/tag/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/space/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/button/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/popconfirm/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/card/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/modal/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/table/index.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,Form,Input,Modal,Popconfirm,Select,Space,Table,Tag,Upload,message!=!antd */ \"(app-pages-browser)/./node_modules/antd/es/upload/index.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/EditOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/DeleteOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/PlusOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/InboxOutlined.js\");\n/* harmony import */ var _barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=DeleteOutlined,EditOutlined,InboxOutlined,PlusOutlined,UploadOutlined!=!@ant-design/icons */ \"(app-pages-browser)/./node_modules/@ant-design/icons/es/icons/UploadOutlined.js\");\n/* harmony import */ var logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! logic-common/dist/components/Notification */ \"(app-pages-browser)/./node_modules/logic-common/dist/components/Notification/index.js\");\n/* harmony import */ var _lib_api_course__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/api/course */ \"(app-pages-browser)/./lib/api/course.ts\");\n/* harmony import */ var _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/api/upload */ \"(app-pages-browser)/./lib/api/upload.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst { Search } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\nconst { Option } = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\nconst CourseManagement = ()=>{\n    _s();\n    const [courseList, setCourseList] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isCourseModalVisible, setIsCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddCourseModalVisible, setIsAddCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isEditCourseModalVisible, setIsEditCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddSeriesModalVisible, setIsAddSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isAddTagModalVisible, setIsAddTagModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishSeriesModalVisible, setIsPublishSeriesModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPublishCourseModalVisible, setIsPublishCourseModalVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editingCourse, setEditingCourse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [searchKeyword, setSearchKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [teachers, setTeachers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseTags, setCourseTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [coverImageUrl, setCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseSeries, setCourseSeries] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseCoverImageUrl, setCourseCoverImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [additionalFiles, setAdditionalFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [courseVideoUrl, setCourseVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseVideoName, setCourseVideoName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentUrl, setCourseDocumentUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseDocumentName, setCourseDocumentName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioUrl, setCourseAudioUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [courseAudioName, setCourseAudioName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoDuration, setVideoDuration] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [editCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [addTagForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishSeriesForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const [publishCourseForm] = _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm();\n    const notification = (0,logic_common_dist_components_Notification__WEBPACK_IMPORTED_MODULE_2__.GetNotification)();\n    // 获取课程列表\n    const fetchCourseList = async ()=>{\n        try {\n            setLoading(true);\n            console.log(\"\\uD83D\\uDCDD 获取课程列表...\");\n            // 这里应该调用获取课程列表的API\n            // 暂时使用空数据，等待后端API实现\n            const mockCourses = [\n                {\n                    id: 1,\n                    title: \"Node.js基础入门\",\n                    name: \"Node.js基础入门\",\n                    seriesTitle: \"Node.js开发系列\",\n                    description: \"Node.js基础知识学习\",\n                    category: \"programming\",\n                    status: \"inactive\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                },\n                {\n                    id: 2,\n                    title: \"Express框架应用\",\n                    name: \"Express框架应用\",\n                    seriesTitle: \"Node.js开发系列\",\n                    description: \"Express框架的使用\",\n                    category: \"programming\",\n                    status: \"active\",\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                }\n            ];\n            setCourseList(mockCourses);\n            setLoading(false);\n        } catch (error) {\n            console.error(\"❌ 获取课程列表失败:\", error);\n            notification.error(\"获取课程列表失败，请重试\");\n            setLoading(false);\n        }\n    };\n    // 添加课程\n    const handleAddCourse = async (values)=>{\n        try {\n            // 构建内容配置，只包含有效的媒体文件\n            const contentConfig = {\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0\n            };\n            if (courseVideoUrl) {\n                contentConfig.video = {\n                    url: courseVideoUrl,\n                    name: courseVideoName || \"课程视频.mp4\"\n                };\n            }\n            if (courseDocumentUrl) {\n                contentConfig.document = {\n                    url: courseDocumentUrl,\n                    name: courseDocumentName || \"课程文档.pdf\"\n                };\n            }\n            if (courseAudioUrl) {\n                contentConfig.audio = {\n                    url: courseAudioUrl,\n                    name: courseAudioName || \"课程音频.mp3\"\n                };\n            }\n            const courseData = {\n                seriesId: parseInt(values.seriesId),\n                title: values.title.trim(),\n                description: values.description.trim(),\n                coverImage: courseCoverImageUrl,\n                hasVideo: courseVideoUrl ? 1 : 0,\n                hasDocument: courseDocumentUrl ? 1 : 0,\n                hasAudio: courseAudioUrl ? 1 : 0,\n                videoDuration: videoDuration || 0,\n                contentConfig,\n                teachingInfo: values.teachingObjectives && values.teachingObjectives.length > 0 ? [\n                    {\n                        title: \"教学目标\",\n                        content: Array.isArray(values.teachingObjectives) ? values.teachingObjectives : [\n                            values.teachingObjectives\n                        ]\n                    }\n                ] : [],\n                additionalResources: additionalFiles.map((file)=>({\n                        title: file.split(\"/\").pop() || \"file\",\n                        url: file,\n                        description: \"课程附件资源\"\n                    })),\n                orderIndex: parseInt(values.orderIndex) || 0\n            };\n            // 验证必要字段\n            if (!courseData.seriesId) {\n                notification.error(\"请选择所属系列课程\");\n                return;\n            }\n            if (!courseData.title) {\n                notification.error(\"请输入课程名称\");\n                return;\n            }\n            if (!courseData.coverImage) {\n                notification.error(\"请上传课程封面\");\n                return;\n            }\n            console.log(\"\\uD83D\\uDCE4 提交课程数据:\", courseData);\n            console.log(\"\\uD83D\\uDCCA 数据大小估算:\", JSON.stringify(courseData).length, \"字符\");\n            // 添加重试机制\n            let retryCount = 0;\n            const maxRetries = 2;\n            let lastError;\n            while(retryCount <= maxRetries){\n                try {\n                    const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourse(courseData);\n                    // 如果成功，跳出重试循环\n                    if (res.code === 200) {\n                        notification.success(\"创建课程成功\");\n                        fetchCourseList();\n                        setIsAddCourseModalVisible(false);\n                        addCourseForm.resetFields();\n                        setCourseCoverImageUrl(\"\");\n                        setAdditionalFiles([]);\n                        setCourseVideoUrl(\"\");\n                        setCourseVideoName(\"\");\n                        setCourseDocumentUrl(\"\");\n                        setCourseDocumentName(\"\");\n                        setCourseAudioUrl(\"\");\n                        setCourseAudioName(\"\");\n                        setVideoDuration(0);\n                        return;\n                    } else {\n                        notification.error(res.msg || \"创建课程失败\");\n                        return;\n                    }\n                } catch (error) {\n                    lastError = error;\n                    retryCount++;\n                    if (retryCount <= maxRetries) {\n                        console.log(\"\\uD83D\\uDD04 第\".concat(retryCount, \"次重试...\"));\n                        notification.warning(\"网络异常，正在重试 (\".concat(retryCount, \"/\").concat(maxRetries, \")\"));\n                        // 等待1秒后重试\n                        await new Promise((resolve)=>setTimeout(resolve, 1000));\n                    }\n                }\n            }\n            // 如果所有重试都失败了，抛出最后的错误\n            throw lastError;\n        } catch (error) {\n            var _error_message, _error_response_data, _error_response, _error_response1, _error_response2, _error_response3, _error_response4, _error_response5;\n            console.error(\"❌ 创建课程失败:\", error);\n            // 更详细的错误处理\n            if (error.code === \"ECONNRESET\" || ((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes(\"ECONNRESET\")) || ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) && error.response.data.message.includes(\"ECONNRESET\")) {\n                notification.error(\"网络连接中断，可能是网络不稳定或服务器繁忙。请稍后重试或联系管理员。\");\n            } else if (error.code === \"NETWORK_ERROR\") {\n                notification.error(\"网络错误，请检查网络连接\");\n            } else if (((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.status) === 413) {\n                notification.error(\"上传文件过大，请压缩后重试\");\n            } else if (((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : _error_response2.status) === 400) {\n                var _error_response_data1, _error_response6;\n                const errorMsg = ((_error_response6 = error.response) === null || _error_response6 === void 0 ? void 0 : (_error_response_data1 = _error_response6.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message;\n                notification.error(\"请求参数错误: \".concat(errorMsg));\n            } else if (((_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : _error_response3.status) === 500) {\n                notification.error(\"服务器内部错误，请联系管理员\");\n            } else {\n                notification.error(\"创建课程失败: \".concat(error.message || \"请稍后重试\"));\n            }\n            console.log(\"\\uD83D\\uDD0D 完整错误信息:\", {\n                message: error.message,\n                code: error.code,\n                status: (_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : _error_response4.status,\n                data: (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : _error_response5.data\n            });\n        }\n    };\n    // 编辑课程\n    const handleEditCourse = async (values)=>{\n        if (!editingCourse) return;\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.updateCourse(editingCourse.id, values);\n            if (res.code === 200) {\n                notification.success(\"更新课程成功\");\n                fetchCourseList();\n                setIsEditCourseModalVisible(false);\n                setEditingCourse(null);\n                editCourseForm.resetFields();\n            } else {\n                notification.error(res.msg || \"更新课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 更新课程失败:\", error);\n            notification.error(\"更新课程失败，请重试\");\n        }\n    };\n    // 删除课程\n    const handleDeleteCourse = async (courseId)=>{\n        try {\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.deleteCourse(courseId);\n            if (res.code === 200) {\n                notification.success(\"删除课程成功\");\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"删除课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 删除课程失败:\", error);\n            notification.error(\"删除课程失败，请重试\");\n        }\n    };\n    // 添加系列课程\n    const handleAddSeries = async (values)=>{\n        try {\n            const seriesData = {\n                ...values,\n                coverImage: coverImageUrl\n            };\n            console.log(\"创建系列课程数据:\", seriesData);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseSeries(seriesData);\n            if (res.code === 200) {\n                notification.success(\"创建系列课程成功\");\n                fetchCourseList();\n                setIsAddSeriesModalVisible(false);\n                addSeriesForm.resetFields();\n                setCoverImageUrl(\"\");\n            } else {\n                notification.error(res.msg || \"创建系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建系列课程失败:\", error);\n            notification.error(\"创建系列课程失败，请重试\");\n        }\n    };\n    // 创建课程标签\n    const handleAddTag = async (values)=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 创建课程标签数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.createCourseTag(values);\n            if (res.code === 200) {\n                notification.success(\"创建标签成功\");\n                setIsAddTagModalVisible(false);\n                addTagForm.resetFields();\n                // 重新获取标签列表\n                fetchCourseTags();\n            } else {\n                notification.error(res.msg || \"创建标签失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 创建标签失败:\", error);\n            notification.error(\"创建标签失败，请重试\");\n        }\n    };\n    // 发布系列课程\n    const handlePublishSeries = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布系列课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourseSeries(values.seriesId);\n            if (res.code === 200) {\n                notification.success(\"发布系列课程成功\");\n                setIsPublishSeriesModalVisible(false);\n                publishSeriesForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，系列信息:\", publishData);\n                // 可以选择显示发布统计信息\n                if (publishData.publishStats) {\n                    const stats = publishData.publishStats;\n                    const statsMessage = \"已发布 \".concat(publishData.publishedCourses, \"/\").concat(publishData.totalCourses, \" 个课程，包含 \").concat(stats.videoCourseCount, \" 个视频课程，总时长 \").concat(Math.round(stats.totalVideoDuration / 60), \" 分钟\");\n                    notification.info(statsMessage);\n                }\n            } else {\n                notification.error(res.msg || \"发布系列课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布系列课程失败:\", error);\n            notification.error(\"发布系列课程失败，请重试\");\n        }\n    };\n    // 发布课程\n    const handlePublishCourse = async (values)=>{\n        try {\n            console.log(\"\\uD83D\\uDCE2 发布课程数据:\", values);\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.publishCourse(values.courseId);\n            if (res.code === 200) {\n                notification.success(\"发布课程成功\");\n                setIsPublishCourseModalVisible(false);\n                publishCourseForm.resetFields();\n                // 显示发布结果信息\n                const publishData = res.data;\n                console.log(\"✅ 发布成功，课程信息:\", publishData);\n                // 刷新课程列表\n                fetchCourseList();\n            } else {\n                notification.error(res.msg || \"发布课程失败\");\n            }\n        } catch (error) {\n            console.error(\"❌ 发布课程失败:\", error);\n            notification.error(\"发布课程失败，请重试\");\n        }\n    };\n    // 处理图片上传\n    const handleImageUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"系列封面图片上传成功，URL:\", url);\n            setCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"图片上传成功\");\n        } catch (error) {\n            console.error(\"系列封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理图片删除\n    const handleImageRemove = async ()=>{\n        setCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理课程封面图片上传\n    const handleCourseCoverUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程封面图片上传成功，URL:\", url);\n            setCourseCoverImageUrl(url);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程封面上传成功\");\n        } catch (error) {\n            console.error(\"课程封面图片上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理课程封面删除\n    const handleCourseCoverRemove = async ()=>{\n        setCourseCoverImageUrl(\"\");\n        return true;\n    };\n    // 处理附件资源上传\n    const handleAdditionalResourceUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            // 调用实际的OSS上传API\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"附件资源上传成功，URL:\", url);\n            setAdditionalFiles((prev)=>[\n                    ...prev,\n                    url\n                ]);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url,\n                name: file.name\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"附件 \".concat(file.name, \" 上传成功\"));\n        } catch (error) {\n            console.error(\"附件资源上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"附件 \".concat(file.name, \" 上传失败: \").concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理附件删除\n    const handleAdditionalResourceRemove = async (file)=>{\n        var _file_response;\n        const url = file.url || ((_file_response = file.response) === null || _file_response === void 0 ? void 0 : _file_response.url);\n        setAdditionalFiles((prev)=>prev.filter((f)=>f !== url));\n        return true;\n    };\n    // 处理视频上传\n    const handleVideoUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程视频上传成功，URL:\", url);\n            setCourseVideoUrl(url);\n            setCourseVideoName(file.name);\n            // 如果是视频文件，尝试获取时长\n            const videoElement = document.createElement(\"video\");\n            videoElement.src = url;\n            videoElement.onloadedmetadata = ()=>{\n                setVideoDuration(Math.floor(videoElement.duration));\n            };\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程视频上传成功\");\n        } catch (error) {\n            console.error(\"课程视频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"视频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理视频删除\n    const handleVideoRemove = async ()=>{\n        setCourseVideoUrl(\"\");\n        setCourseVideoName(\"\");\n        setVideoDuration(0);\n        return true;\n    };\n    // 处理文档上传\n    const handleDocumentUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程文档上传成功，URL:\", url);\n            setCourseDocumentUrl(url);\n            setCourseDocumentName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程文档上传成功\");\n        } catch (error) {\n            console.error(\"课程文档上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"文档上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理文档删除\n    const handleDocumentRemove = async ()=>{\n        setCourseDocumentUrl(\"\");\n        setCourseDocumentName(\"\");\n        return true;\n    };\n    // 处理音频上传\n    const handleAudioUpload = async (options)=>{\n        const { file, onSuccess, onError } = options;\n        try {\n            const url = await _lib_api_upload__WEBPACK_IMPORTED_MODULE_4__.uploadApi.uploadToOss(file);\n            console.log(\"课程音频上传成功，URL:\", url);\n            setCourseAudioUrl(url);\n            setCourseAudioName(file.name);\n            onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess({\n                url: url\n            });\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].success(\"课程音频上传成功\");\n        } catch (error) {\n            console.error(\"课程音频上传失败:\", error);\n            _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_8__[\"default\"].error(\"音频上传失败: \".concat(error.message || \"请稍后重试\"));\n            onError === null || onError === void 0 ? void 0 : onError(error);\n        }\n    };\n    // 处理音频删除\n    const handleAudioRemove = async ()=>{\n        setCourseAudioUrl(\"\");\n        setCourseAudioName(\"\");\n        return true;\n    };\n    // 打开编辑模态框\n    const openEditModal = async (course)=>{\n        setEditingCourse(course);\n        editCourseForm.setFieldsValue(course);\n        setIsEditCourseModalVisible(true);\n    };\n    // 过滤课程列表\n    const filteredCourses = (courseList || []).filter((course)=>course.name.toLowerCase().includes(searchKeyword.toLowerCase()) || course.description.toLowerCase().includes(searchKeyword.toLowerCase()) || course.category.toLowerCase().includes(searchKeyword.toLowerCase()));\n    // 表格列定义\n    const columns = [\n        {\n            title: \"课程名称\",\n            dataIndex: \"name\",\n            key: \"name\"\n        },\n        {\n            title: \"课程描述\",\n            dataIndex: \"description\",\n            key: \"description\",\n            ellipsis: true\n        },\n        {\n            title: \"课程分类\",\n            dataIndex: \"category\",\n            key: \"category\"\n        },\n        {\n            title: \"状态\",\n            dataIndex: \"status\",\n            key: \"status\",\n            render: (status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    color: status === \"active\" ? \"green\" : \"red\",\n                    children: status === \"active\" ? \"启用\" : \"禁用\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 605,\n                    columnNumber: 9\n                }, undefined)\n        },\n        {\n            title: \"创建时间\",\n            dataIndex: \"createdAt\",\n            key: \"createdAt\"\n        },\n        {\n            title: \"操作\",\n            key: \"action\",\n            render: (record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    size: \"middle\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            type: \"link\",\n                            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 19\n                            }, void 0),\n                            onClick: ()=>openEditModal(record),\n                            children: \"编辑\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            title: \"确定要删除这个课程吗？\",\n                            onConfirm: ()=>handleDeleteCourse(record.id),\n                            okText: \"确定\",\n                            cancelText: \"取消\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                type: \"link\",\n                                danger: true,\n                                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 636,\n                                    columnNumber: 21\n                                }, void 0),\n                                children: \"删除\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 633,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 627,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 619,\n                    columnNumber: 9\n                }, undefined)\n        }\n    ];\n    // 获取教师列表\n    // const fetchTeachers = async () => {\n    //   try {\n    //     const { data: res } = await courseApi.getTeachers();\n    //     if (res.code === 200) {\n    //       setTeachers(res.data);\n    //       console.log('成功获取教师列表:', res.data);\n    //     } else {\n    //       console.log('API返回无数据，使用模拟教师数据');\n    //       // 使用模拟数据\n    //       const mockTeachers = [\n    //         { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //         { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //         { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //         { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //         { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //         { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //       ];\n    //       setTeachers(mockTeachers);\n    //     }\n    //   } catch (error) {\n    //     console.error('获取教师列表失败:', error);\n    //     // 使用模拟数据\n    //     const mockTeachers = [\n    //       { id: 1, name: '张老师', email: '<EMAIL>', subject: '数学', school: '实验小学', avatar: '', phone: '13800138001' },\n    //       { id: 2, name: '李老师', email: '<EMAIL>', subject: '语文', school: '实验小学', avatar: '', phone: '13800138002' },\n    //       { id: 3, name: '王老师', email: '<EMAIL>', subject: '英语', school: '第二小学', avatar: '', phone: '13800138003' },\n    //       { id: 4, name: '赵老师', email: '<EMAIL>', subject: '科学', school: '第二小学', avatar: '', phone: '13800138004' },\n    //       { id: 5, name: '刘老师', email: '<EMAIL>', subject: '编程', school: '实验中学', avatar: '', phone: '13800138005' },\n    //       { id: 6, name: '陈老师', email: '<EMAIL>', subject: '信息技术', school: '实验中学', avatar: '', phone: '13800138006' }\n    //     ];\n    //     setTeachers(mockTeachers);\n    //     console.log('使用模拟教师数据:', mockTeachers);\n    //   }\n    // };\n    // 获取课程标签列表 - 使用课程市场API\n    const fetchCourseTags = async ()=>{\n        try {\n            console.log(\"\\uD83C\\uDFF7️ 开始获取课程标签列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getCourseTags({\n                page: 1,\n                pageSize: 100,\n                status: 1 // 只获取启用的标签\n            });\n            console.log(\"\\uD83D\\uDCE8 getCourseTags API响应:\", res);\n            if (res.code === 200 && res.data && res.data.list) {\n                const tags = res.data.list.map((tag)=>({\n                        id: tag.id,\n                        name: tag.name,\n                        color: tag.color,\n                        category: tag.category,\n                        description: tag.description || \"\"\n                    }));\n                setCourseTags(tags);\n                console.log(\"✅ 成功获取课程标签列表:\", tags);\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", res);\n                setCourseTags([]);\n                notification.warning(\"获取标签列表失败，请检查网络连接\");\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程标签失败:\", error);\n            setCourseTags([]);\n            notification.error(\"获取标签列表失败，请重试\");\n        }\n    };\n    // 获取课程系列列表 - 使用课程市场API\n    const fetchCourseSeries = async ()=>{\n        try {\n            var _res_data_pagination, _res_data;\n            console.log(\"\\uD83D\\uDD04 开始获取课程市场系列课程列表...\");\n            const { data: res } = await _lib_api_course__WEBPACK_IMPORTED_MODULE_3__.courseApi.getMarketplaceSeries({\n                page: 1,\n                pageSize: 50 // 课程市场API限制最大50\n            });\n            console.log(\"\\uD83D\\uDCE8 getMarketplaceSeries API响应:\", res);\n            // 检查是否有更多数据\n            if (((_res_data = res.data) === null || _res_data === void 0 ? void 0 : (_res_data_pagination = _res_data.pagination) === null || _res_data_pagination === void 0 ? void 0 : _res_data_pagination.total) > 50) {\n                console.log(\"⚠️ 注意：总共有 \".concat(res.data.pagination.total, \" 个系列课程，当前只显示前50个\"));\n            }\n            if (res.code === 200 && res.data) {\n                console.log(\"\\uD83D\\uDCCA API返回的完整数据结构:\", res.data);\n                if (res.data.list && Array.isArray(res.data.list)) {\n                    console.log(\"\\uD83D\\uDCCB 获取到 \".concat(res.data.list.length, \" 个系列课程\"));\n                    // 将课程市场API返回的数据转换为组件需要的格式\n                    const formattedSeries = res.data.list.map((item, index)=>{\n                        var _item_tags;\n                        console.log(\"\\uD83D\\uDD0D 处理第 \".concat(index + 1, \" 个系列:\"), {\n                            id: item.id,\n                            title: item.title,\n                            category: item.category,\n                            categoryLabel: item.categoryLabel,\n                            tags: item.tags\n                        });\n                        return {\n                            id: item.id,\n                            title: item.title,\n                            description: item.description,\n                            coverImage: item.coverImage || \"\",\n                            category: item.categoryLabel || (item.category === 0 ? \"官方\" : \"社区\"),\n                            teacherIds: [],\n                            tagIds: ((_item_tags = item.tags) === null || _item_tags === void 0 ? void 0 : _item_tags.map((tag)=>tag.id)) || [],\n                            createdAt: item.createdAt || new Date().toISOString(),\n                            updatedAt: item.updatedAt || new Date().toISOString()\n                        };\n                    });\n                    setCourseSeries(formattedSeries);\n                    console.log(\"✅ 成功获取系列课程列表:\", formattedSeries);\n                } else {\n                    console.warn(\"⚠️ API返回数据中没有list字段或list不是数组:\", res.data);\n                    setCourseSeries([]);\n                }\n            } else {\n                console.warn(\"⚠️ API返回数据格式异常:\", {\n                    code: res.code,\n                    message: res.message,\n                    data: res.data\n                });\n                setCourseSeries([]);\n            }\n        } catch (error) {\n            console.error(\"❌ 获取课程系列失败:\", error);\n            setCourseSeries([]);\n            notification.error(\"获取系列课程列表失败，请重试\");\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCourseList();\n        fetchCourseTags();\n        fetchCourseSeries();\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                title: \"课程管理\",\n                extra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    type: \"primary\",\n                    onClick: ()=>{\n                        fetchCourseList();\n                        setIsCourseModalVisible(true);\n                    },\n                    children: \"查看全部\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 794,\n                    columnNumber: 16\n                }, void 0),\n                className: \"shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddCourseModalVisible(true),\n                            children: \"添加课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 801,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddSeriesModalVisible(true),\n                            children: \"添加系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 804,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsAddTagModalVisible(true),\n                            type: \"dashed\",\n                            children: \"添加课程标签\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 807,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishCourseModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 810,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                            block: true,\n                            onClick: ()=>setIsPublishSeriesModalVisible(true),\n                            style: {\n                                backgroundColor: \"white\",\n                                borderColor: \"#d9d9d9\",\n                                color: \"#000000d9\"\n                            },\n                            children: \"发布系列课程\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 813,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 800,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 792,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"课程管理\",\n                open: isCourseModalVisible,\n                onCancel: ()=>setIsCourseModalVisible(false),\n                footer: null,\n                width: 1000,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4 flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Search, {\n                                placeholder: \"搜索课程名称、描述或分类\",\n                                allowClear: true,\n                                style: {\n                                    width: 300\n                                },\n                                onSearch: setSearchKeyword,\n                                onChange: (e)=>setSearchKeyword(e.target.value)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 828,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        type: \"primary\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 838,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setIsAddCourseModalVisible(true),\n                                        children: \"添加课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 836,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        type: \"default\",\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 21\n                                        }, void 0),\n                                        onClick: ()=>setIsAddSeriesModalVisible(true),\n                                        children: \"添加系列课程\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 843,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 835,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 827,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                        columns: columns,\n                        dataSource: filteredCourses,\n                        rowKey: \"id\",\n                        loading: loading,\n                        pagination: {\n                            pageSize: 10,\n                            showSizeChanger: false,\n                            showTotal: (total)=>\"共 \".concat(total, \" 条记录\")\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                        lineNumber: 853,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 820,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"添加课程\",\n                open: isAddCourseModalVisible,\n                onCancel: ()=>{\n                    setIsAddCourseModalVisible(false);\n                    addCourseForm.resetFields();\n                    setCourseCoverImageUrl(\"\");\n                    setAdditionalFiles([]);\n                    setCourseVideoUrl(\"\");\n                    setCourseVideoName(\"\");\n                    setCourseDocumentUrl(\"\");\n                    setCourseDocumentName(\"\");\n                    setCourseAudioUrl(\"\");\n                    setCourseAudioName(\"\");\n                    setVideoDuration(0);\n                },\n                onOk: ()=>addCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"所属系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择所属系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                optionFilterProp: \"children\",\n                                style: {\n                                    width: \"100%\"\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        title: \"\".concat(series.title, \" - \").concat(series.description),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            style: {\n                                                overflow: \"hidden\",\n                                                textOverflow: \"ellipsis\",\n                                                whiteSpace: \"nowrap\",\n                                                maxWidth: \"100%\"\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontWeight: 500\n                                                    },\n                                                    children: series.title\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 911,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    style: {\n                                                        fontSize: \"12px\",\n                                                        color: \"#666\",\n                                                        marginLeft: \"8px\"\n                                                    },\n                                                    children: [\n                                                        \"(\",\n                                                        series.category,\n                                                        \")\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 912,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 905,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, series.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 904,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 897,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 892,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 926,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 921,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述课程内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 934,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 929,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程封面\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传课程封面\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseCover\",\n                                customRequest: handleCourseCoverUpload,\n                                onRemove: handleCourseCoverRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseCoverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: courseCoverImageUrl,\n                                        alt: \"课程封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 956,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 961,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 960,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 963,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 946,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 942,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"orderIndex\",\n                            label: \"课程序号\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程序号\"\n                                },\n                                {\n                                    type: \"number\",\n                                    min: 0,\n                                    message: \"课程序号必须大于等于0\",\n                                    transform: (value)=>Number(value)\n                                }\n                            ],\n                            tooltip: \"在系列课程中的排序位置，数字越小排序越靠前，从0开始\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                type: \"number\",\n                                placeholder: \"请输入课程在系列中的序号（从0开始）\",\n                                min: 0\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 986,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 972,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程视频\",\n                            tooltip: \"上传课程视频文件，系统将自动识别时长等信息\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseVideo\",\n                                customRequest: handleVideoUpload,\n                                onRemove: handleVideoRemove,\n                                accept: \"video/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseVideoUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                                            src: courseVideoUrl,\n                                            style: {\n                                                width: \"100%\",\n                                                maxHeight: \"200px\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseVideoName || \"课程视频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1011,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1005,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1017,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽视频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1020,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP4、AVI、MOV等格式，大小不超过100MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1021,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1016,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 996,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 992,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程文档\",\n                            tooltip: \"上传课程相关文档，如PPT、PDF等\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseDocument\",\n                                customRequest: handleDocumentUpload,\n                                onRemove: handleDocumentRemove,\n                                accept: \".pdf,.doc,.docx,.ppt,.pptx\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseDocumentUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        style: {\n                                            padding: \"20px\",\n                                            textAlign: \"center\"\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                style: {\n                                                    fontSize: \"48px\",\n                                                    color: \"#1890ff\"\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1045,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                style: {\n                                                    marginTop: 8,\n                                                    color: \"#666\"\n                                                },\n                                                children: courseDocumentName || \"课程文档\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1046,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1044,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1043,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1054,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1053,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文档文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1056,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持PDF、Word、PPT格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1052,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1034,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1030,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"课程音频\",\n                            tooltip: \"上传课程音频文件\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"courseAudio\",\n                                customRequest: handleAudioUpload,\n                                onRemove: handleAudioRemove,\n                                accept: \"audio/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: courseAudioUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                            src: courseAudioUrl,\n                                            style: {\n                                                width: \"100%\"\n                                            },\n                                            controls: true\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1080,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            style: {\n                                                marginTop: 8,\n                                                color: \"#666\"\n                                            },\n                                            children: courseAudioName || \"课程音频\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1085,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1079,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1092,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1091,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽音频文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1094,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持MP3、WAV、AAC等格式，大小不超过50MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1095,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1090,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1070,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1066,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"teachingObjectives\",\n                            label: \"教学目标\",\n                            tooltip: \"学员完成本课程后应该达到的学习目标\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"tags\",\n                                placeholder: \"理解Node.js的基本概念和特点，掌握Node.js的安装和环境配置\",\n                                style: {\n                                    width: \"100%\"\n                                }\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1108,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1103,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"附件资源\",\n                            tooltip: \"上传课程相关的附件资源，如PPT、文档、代码等\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    name: \"additionalResources\",\n                                    customRequest: handleAdditionalResourceUpload,\n                                    onRemove: handleAdditionalResourceRemove,\n                                    multiple: true,\n                                    accept: \".pdf,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.zip,.rar,.txt\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {}, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1126,\n                                            columnNumber: 29\n                                        }, void 0),\n                                        children: \"上传附件资源\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1119,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        fontSize: \"12px\",\n                                        color: \"#666\",\n                                        marginTop: 4\n                                    },\n                                    children: \"支持上传PDF、Office文档、压缩包等格式文件，单个文件不超过10MB\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1128,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1115,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 887,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 867,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"编辑课程\",\n                open: isEditCourseModalVisible,\n                onCancel: ()=>{\n                    setIsEditCourseModalVisible(false);\n                    setEditingCourse(null);\n                    editCourseForm.resetFields();\n                },\n                onOk: ()=>editCourseForm.submit(),\n                okText: \"确定\",\n                cancelText: \"取消\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: editCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handleEditCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程名称\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1158,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1153,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程描述\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程描述\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入课程描述\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1166,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1161,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"课程分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程分类\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程基础\",\n                                        children: \"编程基础\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1175,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"编程进阶\",\n                                        children: \"编程进阶\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1176,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"算法思维\",\n                                        children: \"算法思维\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1177,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"项目实战\",\n                                        children: \"项目实战\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1178,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1174,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1169,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"课程状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择课程状态\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"active\",\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1188,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"inactive\",\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1189,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1182,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1148,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1136,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建系列课程\",\n                open: isAddSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsAddSeriesModalVisible(false);\n                    addSeriesForm.resetFields();\n                    setCoverImageUrl(\"\");\n                },\n                onOk: ()=>addSeriesForm.submit(),\n                okText: \"创建系列课程\",\n                cancelText: \"取消\",\n                width: 800,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"title\",\n                            label: \"系列课程名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入系列课程名称\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：React全栈开发实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1219,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1214,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"课程介绍\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程介绍\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 4,\n                                placeholder: \"请详细描述系列课程的内容、目标和特色...\",\n                                showCount: true,\n                                maxLength: 500\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1227,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1222,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            label: \"封面图片\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请上传封面图片\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_19__[\"default\"].Dragger, {\n                                name: \"coverImage\",\n                                customRequest: handleImageUpload,\n                                onRemove: handleImageRemove,\n                                accept: \"image/*\",\n                                maxCount: 1,\n                                listType: \"picture\",\n                                children: coverImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: coverImageUrl,\n                                        alt: \"封面预览\",\n                                        style: {\n                                            width: \"100%\",\n                                            maxHeight: \"200px\",\n                                            objectFit: \"cover\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1249,\n                                        columnNumber: 19\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1248,\n                                    columnNumber: 17\n                                }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-drag-icon\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_DeleteOutlined_EditOutlined_InboxOutlined_PlusOutlined_UploadOutlined_ant_design_icons__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {}, void 0, false, {\n                                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                lineNumber: 1254,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1253,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-text\",\n                                            children: \"点击或拖拽文件到此区域上传\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1256,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"ant-upload-hint\",\n                                            children: \"支持单个文件上传，建议上传jpg、png格式图片，大小不超过2MB\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1257,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1252,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1239,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1235,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"是否为官方系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择是否为官方系列课程\"\n                                }\n                            ],\n                            initialValue: 0,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"是（官方）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1274,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"否（社区）\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1275,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1273,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1267,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"projectMembers\",\n                            label: \"课程成员\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入课程成员\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"请输入课程成员，如：王老师、李助教、张同学\",\n                                showCount: true,\n                                maxLength: 200\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1284,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1279,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"tagIds\",\n                            label: \"标签选择\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                mode: \"multiple\",\n                                placeholder: \"请选择相关标签\",\n                                optionLabelProp: \"label\",\n                                children: courseTags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: tag.id,\n                                        label: tag.name,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            color: tag.color,\n                                            children: tag.name\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1303,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, tag.id, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1302,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1296,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1291,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1209,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1196,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"创建课程标签\",\n                open: isAddTagModalVisible,\n                onCancel: ()=>{\n                    setIsAddTagModalVisible(false);\n                    addTagForm.resetFields();\n                },\n                onOk: ()=>addTagForm.submit(),\n                okText: \"创建标签\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: addTagForm,\n                    layout: \"vertical\",\n                    onFinish: handleAddTag,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"name\",\n                            label: \"标签名称\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请输入标签名称\"\n                                },\n                                {\n                                    max: 20,\n                                    message: \"标签名称不能超过20个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                placeholder: \"例如：高级、编程、实战\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1337,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1329,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"color\",\n                            label: \"标签颜色\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签颜色\"\n                                }\n                            ],\n                            initialValue: \"#007bff\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签颜色\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#007bff\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#007bff\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1349,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"蓝色 (#007bff)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1348,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1347,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#28a745\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#28a745\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1355,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"绿色 (#28a745)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1354,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1353,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#dc3545\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#dc3545\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1361,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"红色 (#dc3545)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1360,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1359,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#ffc107\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#ffc107\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1367,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"黄色 (#ffc107)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1366,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1365,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6f42c1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6f42c1\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1373,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"紫色 (#6f42c1)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1372,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1371,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#fd7e14\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#fd7e14\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1379,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"橙色 (#fd7e14)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1378,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1377,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#20c997\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#20c997\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1385,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"青色 (#20c997)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1384,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1383,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: \"#6c757d\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-4 h-4 rounded\",\n                                                    style: {\n                                                        backgroundColor: \"#6c757d\"\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                                    lineNumber: 1391,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                \"灰色 (#6c757d)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1390,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1389,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1346,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1340,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"category\",\n                            label: \"标签分类\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签分类\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签分类\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"难度标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1405,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"类型标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1406,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 2,\n                                        children: \"特色标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1407,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 3,\n                                        children: \"其他标签\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1408,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1404,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1398,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"description\",\n                            label: \"标签描述\",\n                            rules: [\n                                {\n                                    max: 100,\n                                    message: \"标签描述不能超过100个字符\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                rows: 3,\n                                placeholder: \"请输入标签的详细描述...\",\n                                showCount: true,\n                                maxLength: 100\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1417,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1412,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"status\",\n                            label: \"标签状态\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择标签状态\"\n                                }\n                            ],\n                            initialValue: 1,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择标签状态\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 1,\n                                        children: \"启用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1432,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: 0,\n                                        children: \"禁用\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1433,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1431,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1425,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1324,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1312,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布系列课程\",\n                open: isPublishSeriesModalVisible,\n                onCancel: ()=>{\n                    setIsPublishSeriesModalVisible(false);\n                    publishSeriesForm.resetFields();\n                },\n                onOk: ()=>publishSeriesForm.submit(),\n                okText: \"发布系列\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishSeriesForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishSeries,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"seriesId\",\n                            label: \"选择要发布的系列课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的系列课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择系列课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseSeries.map((series)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: series.id,\n                                        children: [\n                                            series.title,\n                                            \" (\",\n                                            series.category,\n                                            \")\"\n                                        ]\n                                    }, series.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1470,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1462,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1457,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1482,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1477,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1491,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后系列课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1493,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有已完成的课程才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1494,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看详细的发布统计信息\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1495,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1496,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1492,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1490,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1452,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1440,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                title: \"发布课程\",\n                open: isPublishCourseModalVisible,\n                onCancel: ()=>{\n                    setIsPublishCourseModalVisible(false);\n                    publishCourseForm.resetFields();\n                },\n                onOk: ()=>publishCourseForm.submit(),\n                okText: \"发布课程\",\n                cancelText: \"取消\",\n                width: 600,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    form: publishCourseForm,\n                    layout: \"vertical\",\n                    onFinish: handlePublishCourse,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"courseId\",\n                            label: \"选择要发布的课程\",\n                            rules: [\n                                {\n                                    required: true,\n                                    message: \"请选择要发布的课程\"\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                placeholder: \"请选择课程\",\n                                showSearch: true,\n                                filterOption: (input, option)=>{\n                                    var _option_children;\n                                    return option === null || option === void 0 ? void 0 : (_option_children = option.children) === null || _option_children === void 0 ? void 0 : _option_children.toLowerCase().includes(input.toLowerCase());\n                                },\n                                children: courseList.map((course)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Option, {\n                                        value: course.id,\n                                        children: [\n                                            course.title || course.name,\n                                            \" (\",\n                                            course.seriesTitle || \"未分类\",\n                                            \")\"\n                                        ]\n                                    }, course.id, true, {\n                                        fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                        lineNumber: 1533,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1525,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1520,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].Item, {\n                            name: \"publishNote\",\n                            label: \"发布说明\",\n                            rules: [\n                                {\n                                    required: false\n                                }\n                            ],\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_5__[\"default\"].TextArea, {\n                                placeholder: \"请输入发布说明（可选）\",\n                                rows: 3,\n                                maxLength: 200,\n                                showCount: true\n                            }, void 0, false, {\n                                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                lineNumber: 1545,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1540,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-gray-50 p-4 rounded-lg\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"text-sm font-medium text-gray-700 mb-2\",\n                                    children: \"发布说明：\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1554,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm text-gray-600 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后课程将在课程市场中公开显示\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1556,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 只有完整的课程内容才会被发布\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1557,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布后可以查看课程的访问统计\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1558,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• 发布状态可以随时修改\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                            lineNumber: 1559,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                                    lineNumber: 1555,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                            lineNumber: 1553,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                    lineNumber: 1515,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\logicleap\\\\logicleapweb\\\\app\\\\admin-space\\\\components\\\\course-management.tsx\",\n                lineNumber: 1503,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(CourseManagement, \"h5/Z7Q+yaU+v+dRwlYBMJomGAsc=\", false, function() {\n    return [\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm,\n        _barrel_optimize_names_Button_Card_Form_Input_Modal_Popconfirm_Select_Space_Table_Tag_Upload_message_antd__WEBPACK_IMPORTED_MODULE_7__[\"default\"].useForm\n    ];\n});\n_c = CourseManagement;\n/* harmony default export */ __webpack_exports__[\"default\"] = (CourseManagement);\nvar _c;\n$RefreshReg$(_c, \"CourseManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/admin-space/components/course-management.tsx\n"));

/***/ })

});